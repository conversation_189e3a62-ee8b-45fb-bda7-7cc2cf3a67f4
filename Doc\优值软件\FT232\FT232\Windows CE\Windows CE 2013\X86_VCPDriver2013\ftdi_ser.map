 ftdi_ser

 Timestamp is 5735dde3 (Fri May 13 15:00:03 2016)

 Preferred load address is 10000000

 Start         Length     Name                   Class
 0001:00000000 00000b08H .rdata                  CODE
 0001:00000b08 00000038H .rdata$debug            CODE
 0001:00000b40 00000004H .rdata$sxdata           CODE
 0001:00000b44 0000a8b4H .text                   CODE
 0001:0000b3f8 0000014cH .xdata$x                CODE
 0001:0000b544 00000028H .idata$2                CODE
 0001:0000b56c 00000014H .idata$3                CODE
 0001:0000b580 00000124H .idata$4                CODE
 0001:0000b6a4 00000018H .idata$6                CODE
 0001:0000b6c0 00000156H .edata                  CODE
 0002:00000000 00000124H .idata$5                DATA
 0002:00000128 00000110H .data                   DATA
 0002:00000240 00000754H .bss                    DATA
 0003:00000000 00000058H .rsrc$01                DATA
 0003:00000060 00000328H .rsrc$02                DATA

  Address         Publics by Value              Rva+Base       Lib:Object

 0000:00000001       ___safe_se_handler_count   00000001     <absolute>
 0001:00000038       ??_C@_1CC@KMPFMANC@?$AAD?$AAe?$AAv?$AAi?$AAc?$AAe?$AAA?$AAr?$AAr?$AAa?$AAy?$AAI?$AAn?$AAd?$AAe?$AAx?$AA?$AA@ 10001038     mdd.obj
 0001:0000005c       ??_C@_1BI@BHLBGEOK@?$AAP?$AAr?$AAi?$AAo?$AAr?$AAi?$AAt?$AAy?$AA2?$AA5?$AA6?$AA?$AA@ 1000105c     mdd.obj
 0001:00000078       _IoVTbl                    10001078     ftdi_ser.obj
 0001:000000f0       ??_C@_1M@DFKENGJN@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs?$AA?$AA@ 100010f0     ftdi_ser.obj
 0001:000000fc       ??_C@_1CE@ILLNCEFP@?$AAA?$AAc?$AAt?$AAi?$AAv?$AAe?$AAS?$AAy?$AAn?$AAc?$AAS?$AAe?$AAt?$AAt?$AAi?$AAn?$AAg?$AA?$AA@ 100010fc     ftdi_ser.obj
 0001:00000120       ??_C@_17KACEIPNC@?$AAK?$AAe?$AAy?$AA?$AA@ 10001120     ftdi_ser.obj
 0001:00000128       ??_C@_1BK@GLGKBGNH@?$AAL?$AAa?$AAt?$AAe?$AAn?$AAc?$AAy?$AAT?$AAi?$AAm?$AAe?$AAr?$AA?$AA@ 10001128     ftdi_ser.obj
 0001:00000144       ??_C@_1BO@FLELEHKB@?$AAI?$AAn?$AAT?$AAr?$AAa?$AAn?$AAs?$AAf?$AAe?$AAr?$AAS?$AAi?$AAz?$AAe?$AA?$AA@ 10001144     ftdi_ser.obj
 0001:00000164       ??_C@_1CA@CBPLKEPJ@?$AAO?$AAu?$AAt?$AAT?$AAr?$AAa?$AAn?$AAs?$AAf?$AAe?$AAr?$AAS?$AAi?$AAz?$AAe?$AA?$AA@ 10001164     ftdi_ser.obj
 0001:00000184       ??_C@_1BG@IFDFFDLO@?$AAC?$AAo?$AAn?$AAf?$AAi?$AAg?$AAD?$AAa?$AAt?$AAa?$AA?$AA@ 10001184     ftdi_ser.obj
 0001:0000019c       ??_C@_1BM@JCMCOHGE@?$AAD?$AAe?$AAv?$AAi?$AAc?$AAe?$AAC?$AAo?$AAn?$AAt?$AAe?$AAx?$AAt?$AA?$AA@ 1000119c     ftdi_ser.obj
 0001:000001b8       ??_C@_1BM@KEONHDCJ@?$AAE?$AAm?$AAu?$AAl?$AAa?$AAt?$AAi?$AAo?$AAn?$AAM?$AAo?$AAd?$AAe?$AA?$AA@ 100011b8     ftdi_ser.obj
 0001:000001d4       ??_C@_1CG@MMMJBOMI@?$AAm?$AAs?$AAS?$AAl?$AAe?$AAe?$AAp?$AAA?$AAf?$AAt?$AAe?$AAr?$AAB?$AAu?$AAl?$AAk?$AAI?$AAn?$AA?$AA@ 100011d4     ftdi_ser.obj
 0001:000001fc       ??_C@_1CA@KIFMDICO@?$AAC?$AAo?$AAn?$AAf?$AAi?$AAg?$AAD?$AAa?$AAt?$AAa?$AAF?$AAl?$AAa?$AAg?$AAs?$AA?$AA@ 100011fc     ftdi_ser.obj
 0001:0000021c       ??_C@_1BK@HLLNPFIM@?$AAB?$AAu?$AAl?$AAk?$AAP?$AAr?$AAi?$AAo?$AAr?$AAi?$AAt?$AAy?$AA?$AA@ 1000121c     ftdi_ser.obj
 0001:00000238       ??_C@_1BO@HDCCNDFF@?$AAB?$AAu?$AAl?$AAk?$AAP?$AAr?$AAi?$AAo?$AAr?$AAi?$AAt?$AAy?$AAE?$AAx?$AA?$AA@ 10001238     ftdi_ser.obj
 0001:00000258       ??_C@_1BM@ONMIKGLP@?$AAB?$AAu?$AAl?$AAk?$AAT?$AAi?$AAm?$AAe?$AAS?$AAl?$AAi?$AAc?$AAe?$AA?$AA@ 10001258     ftdi_ser.obj
 0001:00000274       _gszArrayIndex             10001274     ftdi_usb.obj
 0001:00000298       _gszVersion                10001298     ftdi_usb.obj
 0001:000002a8       _gszVersionNumber          100012a8     ftdi_usb.obj
 0001:000002bc       _gcszUnRegisterClientDriverId 100012bc     ftdi_usb.obj
 0001:000002f0       _gcszUnRegisterClientSettings 100012f0     ftdi_usb.obj
 0001:00000324       _gcszFTDIUSBDriverId       10001324     ftdi_usb.obj
 0001:0000033c       ??_C@_17CHLIBJCN@?$AA?$CF?$AAd?$AA?3?$AA?$AA@ 1000133c     ftdi_usb.obj
 0001:00000344       ??_C@_15GANGMFKL@?$AA?$CF?$AAs?$AA?$AA@ 10001344     ftdi_usb.obj
 0001:0000034c       ??_C@_1BK@BPGMFHJH@?$AAI?$AAn?$AAi?$AAt?$AAi?$AAa?$AAl?$AAI?$AAn?$AAd?$AAe?$AAx?$AA?$AA@ 1000134c     ftdi_usb.obj
 0001:00000368       ??_C@_1BO@LFKIOHPI@?$AAd?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?2?$AAa?$AAc?$AAt?$AAi?$AAv?$AAe?$AA?$AA@ 10001368     ftdi_usb.obj
 0001:00000388       ??_C@_1CE@EBNPANE@?$AAd?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?2?$AAa?$AAc?$AAt?$AAi?$AAv?$AAe?$AA?2?$AA?$CF?$AAs?$AA?$AA@ 10001388     ftdi_usb.obj
 0001:000003ac       ??_C@_19DINFBLAK@?$AAN?$AAa?$AAm?$AAe?$AA?$AA@ 100013ac     ftdi_usb.obj
 0001:000003b8       ??_C@_1BC@HPDGIPOA@?$AAV?$AAe?$AAn?$AAd?$AAo?$AAr?$AAI?$AAd?$AA?$AA@ 100013b8     ftdi_usb.obj
 0001:000003cc       ??_C@_17OGBPPHLA@?$AAH?$AAn?$AAd?$AA?$AA@ 100013cc     ftdi_usb.obj
 0001:000003d4       ??_C@_1BC@CGPKCBFG@?$AAF?$AAu?$AAl?$AAl?$AAN?$AAa?$AAm?$AAe?$AA?$AA@ 100013d4     ftdi_usb.obj
 0001:000003e8       ??_C@_1BC@OMMMELN@?$AAu?$AAs?$AAb?$AAd?$AA?4?$AAd?$AAl?$AAl?$AA?$AA@ 100013e8     ftdi_usb.obj
 0001:000003fc       ??_C@_0BG@INDJIEMK@?2Windows?2FTDIPORT?4INF?$AA@ 100013fc     ftdi_usb.obj
 0001:00000414       ??_C@_1CO@OHLKFGFN@?$AAR?$AAe?$AAg?$AAi?$AAs?$AAt?$AAe?$AAr?$AAC?$AAl?$AAi?$AAe?$AAn?$AAt?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AAI?$AAD?$AA?$AA@ 10001414     ftdi_usb.obj
 0001:00000444       ??_C@_1CO@GPOIHENG@?$AAR?$AAe?$AAg?$AAi?$AAs?$AAt?$AAe?$AAr?$AAC?$AAl?$AAi?$AAe?$AAn?$AAt?$AAS?$AAe?$AAt?$AAt?$AAi?$AAn?$AAg?$AAs?$AA?$AA@ 10001444     ftdi_usb.obj
 0001:00000474       ??_C@_17DJMMGLBL@?$AAD?$AAl?$AAl?$AA?$AA@ 10001474     ftdi_usb.obj
 0001:0000047c       ??_C@_1O@GBAEOCCH@?$AAP?$AAr?$AAe?$AAf?$AAi?$AAx?$AA?$AA@ 1000147c     ftdi_usb.obj
 0001:00000490       ??_C@_1HM@CMHCAGJO@?$AAc?$AA?3?$AA?2?$AAw?$AAi?$AAn?$AAc?$AAe?$AA8?$AA0?$AA0?$AA?2?$AAo?$AAs?$AAd?$AAe?$AAs?$AAi?$AAg?$AAn?$AAs?$AA?2?$AAv?$AAc?$AAp?$AAo?$AAs?$AAc?$AAe?$AAp?$AAc?$AA?2@ 10001490     ftdi_usb.obj
 0001:0000050c       ??_C@_1BK@DIMGGMC@?$AAF?$AAT?$AAD?$AAI?$AA_?$AAS?$AAE?$AAR?$AA?4?$AAD?$AAL?$AAL?$AA?$AA@ 1000150c     ftdi_usb.obj
 0001:00000528       ??_C@_1FO@MLCJFIIN@?$AA?$CF?$AAs?$AA?3?$AA?5?$AAC?$AAo?$AAd?$AAe?$AA?5?$AAC?$AAo?$AAv?$AAe?$AAr?$AAa?$AAg?$AAe?$AA?5?$AAT?$AAr?$AAa?$AAp?$AA?5?$AAi?$AAn?$AA?3?$AA?5?$AAf?$AAt?$AAd?$AAi?$AA_@ 10001528     ftdi_usb.obj
 0001:00000588       ??_C@_1BI@DFKEDNPO@?$AAB?$AAu?$AAl?$AAk?$AAI?$AAn?$AAF?$AAl?$AAa?$AAg?$AAs?$AA?$AA@ 10001588     ftdi_usb.obj
 0001:000005a0       ??_C@_1BK@KDFGCGI@?$AAB?$AAu?$AAl?$AAk?$AAO?$AAu?$AAt?$AAF?$AAl?$AAa?$AAg?$AAs?$AA?$AA@ 100015a0     ftdi_usb.obj
 0001:000005bc       ??_C@_1CA@CFOPMDEA@?$AAS?$AAy?$AAn?$AAc?$AAh?$AAr?$AAo?$AAn?$AAo?$AAu?$AAs?$AAB?$AAu?$AAl?$AAk?$AA?$AA@ 100015bc     ftdi_usb.obj
 0001:000005dc       ??_C@_1CA@PBBEBOEE@?$AAM?$AAi?$AAn?$AAW?$AAr?$AAi?$AAt?$AAe?$AAT?$AAi?$AAm?$AAe?$AAo?$AAu?$AAt?$AA?$AA@ 100015dc     ftdi_usb.obj
 0001:000005fc       ??_C@_1BO@ECPAAEBI@?$AAM?$AAi?$AAn?$AAR?$AAe?$AAa?$AAd?$AAT?$AAi?$AAm?$AAe?$AAo?$AAu?$AAt?$AA?$AA@ 100015fc     ftdi_usb.obj
 0001:0000061c       ??_C@_1M@EENNDIOJ@?$AAI?$AAn?$AAd?$AAe?$AAx?$AA?$AA@ 1000161c     ftdi_usb.obj
 0001:00000628       ??_C@_1M@ILHOPKA@?$AAE?$AAr?$AAr?$AAo?$AAr?$AA?$AA@ 10001628     ftdi_usb.obj
 0001:00000638       ??_C@_1FI@DGBDABNL@?$AAD?$AAe?$AAv?$AAi?$AAc?$AAe?$AA?5?$AAn?$AAo?$AAt?$AA?5?$AAi?$AAn?$AAs?$AAt?$AAa?$AAl?$AAl?$AAe?$AAd?$AA?5?$AA?9?$AA?5?$AAf?$AAr?$AAe?$AAe?$AA?5?$AAi?$AAn?$AAd?$AAe@ 10001638     ftdi_usb.obj
 0001:00000690       ??_C@_19HEPLDNLP@?$AA?$CF?$AAs?$AA?$CF?$AAd?$AA?$AA@ 10001690     ftdi_usb.obj
 0001:0000069c       ??_C@_1M@KEKDJCFI@?$AA?$CF?$AAs?$AA?$CF?$AAd?$AA?3?$AA?$AA@ 1000169c     ftdi_usb.obj
 0001:000006a8       ??_C@_1BK@GBMCOKGG@?$AAS?$AAe?$AAr?$AAi?$AAa?$AAl?$AAN?$AAu?$AAm?$AAb?$AAe?$AAr?$AA?$AA@ 100016a8     ftdi_usb.obj
 0001:000006c4       ??_C@_1BI@DLMANABL@?$AAD?$AAe?$AAs?$AAc?$AAr?$AAi?$AAp?$AAt?$AAi?$AAo?$AAn?$AA?$AA@ 100016c4     ftdi_usb.obj
 0001:000006dc       ??_C@_1BC@IAEGNIJM@?$AAC?$AAh?$AAi?$AAp?$AAT?$AAy?$AAp?$AAe?$AA?$AA@ 100016dc     ftdi_usb.obj
 0001:000006f0       ??_C@_1BI@IMELEEEO@?$AAF?$AAT?$AA?5?$AAX?$AA?5?$AAS?$AAe?$AAr?$AAi?$AAe?$AAs?$AA?$AA@ 100016f0     ftdi_usb.obj
 0001:00000708       ??_C@_1O@NIBEILOD@?$AAF?$AAT?$AA2?$AA3?$AA2?$AAH?$AA?$AA@ 10001708     ftdi_usb.obj
 0001:00000718       ??_C@_1BA@DHHFLCKG@?$AAF?$AAT?$AA4?$AA2?$AA3?$AA2?$AAH?$AA?$AA@ 10001718     ftdi_usb.obj
 0001:00000728       ??_C@_1BA@NIEIBHEG@?$AAF?$AAT?$AA2?$AA2?$AA3?$AA2?$AAH?$AA?$AA@ 10001728     ftdi_usb.obj
 0001:00000738       ??_C@_1O@OHLADMBI@?$AAF?$AAT?$AA2?$AA3?$AA2?$AAR?$AA?$AA@ 10001738     ftdi_usb.obj
 0001:00000748       ??_C@_1O@NLGABICA@?$AAF?$AAT?$AA2?$AA2?$AA3?$AA2?$AA?$AA@ 10001748     ftdi_usb.obj
 0001:00000758       ??_C@_1O@LHKJGLIH@?$AAF?$AAT?$AA2?$AA3?$AA2?$AAB?$AA?$AA@ 10001758     ftdi_usb.obj
 0001:00000768       ??_C@_1BE@OOJAEBBK@?$AAF?$AAT?$AA8?$AAU?$AA2?$AA3?$AA2?$AAA?$AAM?$AA?$AA@ 10001768     ftdi_usb.obj
 0001:0000077c       ??_C@_1BA@LEPJIIOK@?$AAU?$AAn?$AAk?$AAn?$AAo?$AAw?$AAn?$AA?$AA@ 1000177c     ftdi_usb.obj
 0001:0000078c       ??_C@_1BC@LKPHGMEE@?$AAU?$AAs?$AAb?$AAS?$AAp?$AAe?$AAe?$AAd?$AA?$AA@ 1000178c     ftdi_usb.obj
 0001:000007a0       ??_C@_1BC@GCEGDIAC@?$AAH?$AAi?$AA?9?$AAS?$AAp?$AAe?$AAe?$AAd?$AA?$AA@ 100017a0     ftdi_usb.obj
 0001:000007b4       ??_C@_1BG@NNDEAGOP@?$AAF?$AAu?$AAl?$AAl?$AA?9?$AAS?$AAp?$AAe?$AAe?$AAd?$AA?$AA@ 100017b4     ftdi_usb.obj
 0001:000007cc       ??_C@_1BE@PJECJHAI@?$AAP?$AAr?$AAo?$AAd?$AAu?$AAc?$AAt?$AAI?$AAd?$AA?$AA@ 100017cc     ftdi_usb.obj
 0001:000007e0       ??_C@_1CI@DNFHOJGM@?$AAb?$AAC?$AAo?$AAn?$AAf?$AAi?$AAg?$AAu?$AAr?$AAa?$AAt?$AAi?$AAo?$AAn?$AAV?$AAa?$AAl?$AAu?$AAe?$AA?$AA@ 100017e0     usbddrv.obj
 0001:00000808       ??_C@_13FPGAJAPJ@?$AA?2?$AA?$AA@ 10001808     usbddrv.obj
 0001:0000080c       ??_C@_1DA@HOLFMPAN@?$AAI?$AAV?$AAT?$AA?5?$AAI?$AAn?$AAv?$AAa?$AAl?$AAi?$AAd?$AA?5?$AAH?$AAa?$AAn?$AAd?$AAl?$AAe?$AA?5?$AA?$CF?$AAp?$AA?$AN?$AA?6?$AA?$AA@ 1000180c     usbddrv.obj
 0001:0000083c       ??_C@_1BI@NIBKFDCC@?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?2?$AAU?$AAS?$AAB?$AA?$AA@ 1000183c     usbd.obj
 0001:00000854       ??_C@_1BM@EKFDCEJD@?$AAC?$AAl?$AAi?$AAe?$AAn?$AAt?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?$AA@ 10001854     usbd.obj
 0001:00000870       ??_C@_1BI@PGFBCHEP@?$AAL?$AAo?$AAa?$AAd?$AAC?$AAl?$AAi?$AAe?$AAn?$AAt?$AAs?$AA?$AA@ 10001870     usbd.obj
 0001:00000888       ??_C@_1BA@GHOECOCL@?$AAD?$AAe?$AAf?$AAa?$AAu?$AAl?$AAt?$AA?$AA@ 10001888     usbd.obj
 0001:00000898       ??_C@_17JOFCMBBD@?$AAD?$AAL?$AAL?$AA?$AA@ 10001898     usbd.obj
 0001:000008a0       ??_C@_1CA@PKLHEOKA@?$AAU?$AAS?$AAB?$AAD?$AAe?$AAv?$AAi?$AAc?$AAe?$AAA?$AAt?$AAt?$AAa?$AAc?$AAh?$AA?$AA@ 100018a0     usbd.obj
 0001:000008c0       ??_C@_1CC@BDLJNJJG@?$AAU?$AAS?$AAB?$AAI?$AAn?$AAs?$AAt?$AAa?$AAl?$AAl?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AA?$AA@ 100018c0     usbd.obj
 0001:000008e4       ??_C@_1BC@OCIMDPHE@?$AA?$CF?$AAu?$AA_?$AA?$CF?$AAu?$AA_?$AA?$CF?$AAu?$AA?$AA@ 100018e4     usbd.obj
 0001:000008f8       ??_C@_1M@JDFOPOOF@?$AA?$CF?$AAu?$AA_?$AA?$CF?$AAu?$AA?$AA@ 100018f8     usbd.obj
 0001:00000904       ??_C@_15EFLNJKHH@?$AA?$CF?$AAu?$AA?$AA@ 10001904     usbd.obj
 0001:0000090c       ??_C@_1BO@LBHFGNBG@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs?$AA?$AA@ 1000190c     usbd.obj
 0001:0000092c       ??_C@_1CM@OPGBINKG@?$AAV?$AAD?$AAH?$AA?5?$AAI?$AAn?$AAv?$AAa?$AAl?$AAi?$AAd?$AA?5?$AA?$CF?$AAp?$AA?5?$AA?$CI?$AA?$CF?$AAx?$AA?$CJ?$AA?$AN?$AA?6?$AA?$AA@ 1000192c     usbd.obj
 0001:00000958       ??_C@_1CG@PAPLOCPN@?$AAV?$AAD?$AAH?$AA?5?$AAE?$AAx?$AAc?$AAe?$AAp?$AAt?$AAi?$AAo?$AAn?$AA?5?$AA?$CF?$AAp?$AA?$AN?$AA?6?$AA?$AA@ 10001958     usbd.obj
 0001:00000980       ??_C@_03HMFOOINA@r?$CLb?$AA@ 10001980     MultiPlatformFile.obj
 0001:00000a20       ??_C@_07FCMOEAAJ@VID_?$CFlX?$AA@ 10001a20     INFParse.obj
 0001:00000a28       ??_C@_07JEKBEJIO@PID_?$CFlX?$AA@ 10001a28     INFParse.obj
 0001:00000a30       ??_C@_01OHGJGJJP@?$FL?$AA@ 10001a30     INFParse.obj
 0001:00000a34       ??_C@_02GENKJLAO@?$FN?$AA?$AA@ 10001a34     INFParse.obj
 0001:00000a38       ??_C@_03JALODAI@?$CFld?$AA@ 10001a38     INFParse.obj
 0001:00000a3c       ??_C@_02EMFKHFLK@?$CFX?$AA@ 10001a3c     INFParse.obj
 0001:00000a40       ??_C@_03HEFJANMG@HKR?$AA@  10001a40     INFParse.obj
 0001:00000a44       ??_C@_01IHBHIGKO@?0?$AA@   10001a44     INFParse.obj
 0001:00000a48       ??_C@_03HKPIJKPH@?$CFlX?$AA@ 10001a48     INFParse.obj
 0001:00000a4c       ??_C@_0P@EOLGHOHJ@?$FLManufacturer?$FN?$AA@ 10001a4c     INFParse.obj
 0001:00000a5c       ??_C@_05GCELKHFA@?$FL?$CFs?$CFs?$AA@ 10001a5c     INFParse.obj
 0001:00000a68       ??_C@_1EM@LDNEMOFO@?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?2?$AAU?$AAS?$AAB?$AA?2?$AAC?$AAl?$AAi?$AAe?$AAn?$AAt?$AAD?$AAr?$AAi?$AAv?$AAe?$AAr?$AAs?$AA?2?$AAF?$AAT?$AAD?$AAI?$AA_?$AAD@ 10001a68     INFParse.obj
 0001:00000ab4       ??_C@_17KKLPNNMB@?$AAC?$AAO?$AAM?$AA?$AA@ 10001ab4     INFParse.obj
 0001:00000ac0       __load_config_used         10001ac0     ccrt0:loadcfg.obj
 0001:00000b40       ___safe_se_handler_table   10001b40     <linker-defined>
 0001:00000b44       _ApplyDCB                  10001b44 f   mdd.obj
 0001:00000ce2       _COM_Close                 10001ce2 f   mdd.obj
 0001:00000e79       _COM_Deinit                10001e79 f   mdd.obj
 0001:00000f68       _COM_IOControl             10001f68 f   mdd.obj
 0001:00001639       _COM_Init                  10002639 f   mdd.obj
 0001:00001876       _COM_Open                  10002876 f   mdd.obj
 0001:00001a91       _COM_PowerDown             10002a91 f   mdd.obj
 0001:00001aaf       _COM_PowerUp               10002aaf f   mdd.obj
 0001:00001acd       _COM_Read                  10002acd f   mdd.obj
 0001:00001dcb       _COM_Seek                  10002dcb f   mdd.obj
 0001:00001dcf       _COM_Write                 10002dcf f   mdd.obj
 0001:00001f83       _DllEntry                  10002f83 f   mdd.obj
 0001:00001fba       _DoTxData                  10002fba f   mdd.obj
 0001:000020a6       _EvaluateEventFlag         100030a6 f   mdd.obj
 0001:00002126       _ProcessExiting            10003126 f   mdd.obj
 0001:00002221       _RxBytesAvail              10003221 f i mdd.obj
 0001:0000228d       _SerialEventHandler        1000328d f   mdd.obj
 0001:00002529       _StartDispatchThread       10003529 f   mdd.obj
 0001:0000255d       _StopDispatchThread        1000355d f   mdd.obj
 0001:000025b9       _WaitCommEvent             100035b9 f   mdd.obj
 0001:000029a0       _SerClearDTR               100039a0 f   ftdi_ser.obj
 0001:000029cc       _SerSetDTR                 100039cc f   ftdi_ser.obj
 0001:000029f8       _SerClearRTS               100039f8 f   ftdi_ser.obj
 0001:00002a24       _SerSetRTS                 10003a24 f   ftdi_ser.obj
 0001:00002a50       _SerClearBreak             10003a50 f   ftdi_ser.obj
 0001:00002a81       _SerSetBreak               10003a81 f   ftdi_ser.obj
 0001:00002ab5       _SerSetDCB                 10003ab5 f   ftdi_ser.obj
 0001:00002b82       _SerSetCommTimeouts        10003b82 f   ftdi_ser.obj
 0001:00002b9b       _SerGetRxBufferSize        10003b9b f   ftdi_ser.obj
 0001:00002b9e       _SerGetInterruptType       10003b9e f   ftdi_ser.obj
 0001:00002be0       _SerRxIntr                 10003be0 f   ftdi_ser.obj
 0001:00002dc3       _SerTxIntr                 10003dc3 f   ftdi_ser.obj
 0001:00002f44       _SerLineIntr               10003f44 f   ftdi_ser.obj
 0001:00002fad       _SerModemIntr              10003fad f   ftdi_ser.obj
 0001:0000305e       _SerGetStatus              1000405e f   ftdi_ser.obj
 0001:000030a5       _SerReset                  100040a5 f   ftdi_ser.obj
 0001:000030d8       _SerGetModemStatus         100040d8 f   ftdi_ser.obj
 0001:000030f2       _SerPurgeComm              100040f2 f   ftdi_ser.obj
 0001:00003167       _SerXmitComChar            10004167 f   ftdi_ser.obj
 0001:000031b9       _SerIoctl                  100041b9 f   ftdi_ser.obj
 0001:0000355c       _ActiveSyncMonitor         1000455c f   ftdi_ser.obj
 0001:000035d3       _CleanupOpenDevice         100045d3 f   ftdi_ser.obj
 0001:0000369b       _ConfigureActiveSyncMonitor 1000469b f   ftdi_ser.obj
 0001:0000384c       _FTDIReadTransferComplete  1000484c f   BULK_IN.obj
 0001:0000384c       _CLIDefaultTransferComplete 1000484c f   usbclient.obj
 0001:0000384c       _FTDIWriteTransferComplete 1000484c f   ftdi_ser.obj
 0001:00003881       _GetSerialObject           10004881 f   ftdi_ser.obj
 0001:0000389c       _InitialiseOpenDevice      1000489c f   ftdi_ser.obj
 0001:00003e6a       _OutRequest                10004e6a f   ftdi_ser.obj
 0001:00003ecd       _RegQueryDWORD             10004ecd f   ftdi_ser.obj
 0001:00003ef9       _RegRemoveDeviceContext    10004ef9 f   ftdi_ser.obj
 0001:00003fe8       _Ser_GetRegistryData       10004fe8 f   ftdi_ser.obj
 0001:000040a8       _SetDeviceLoading          100050a8 f   ftdi_ser.obj
 0001:000040d6       _SetDeviceUnloading        100050d6 f   ftdi_ser.obj
 0001:00004107       _SetFlowParams             10005107 f   ftdi_ser.obj
 0001:00004156       _StartEventThread          10005156 f   ftdi_ser.obj
 0001:0000418f       _TerminateActiveSyncMonitor 1000518f f   ftdi_ser.obj
 0001:000041e3       _FT_ClrDtr                 100051e3 f   ftdi_utils.obj
 0001:0000429d       _FT_ClrRts                 1000529d f   ftdi_utils.obj
 0001:00004354       _FT_GetBitMode             10005354 f   ftdi_utils.obj
 0001:000043f6       _FT_GetDeviceDescription   100053f6 f   ftdi_utils.obj
 0001:0000441d       _FT_GetDeviceSerialNumber  1000541d f   ftdi_utils.obj
 0001:00004444       _FT_GetLatencyTimer        10005444 f   ftdi_utils.obj
 0001:000044e6       _FT_GetModemStatus         100054e6 f   ftdi_utils.obj
 0001:0000452c       _FT_GetStringDescriptor    1000552c f   ftdi_utils.obj
 0001:00004715       _FT_Purge                  10005715 f   ftdi_utils.obj
 0001:0000478e       _FT_ResetHardware          1000578e f   ftdi_utils.obj
 0001:0000481e       _FT_ResetPipe              1000581e f   ftdi_utils.obj
 0001:00004876       _FT_RestoreDeviceSettings  10005876 f   ftdi_utils.obj
 0001:000048e5       _FT_SetBaudRate            100058e5 f   ftdi_utils.obj
 0001:00004bec       _FT_SetBitMode             10005bec f   ftdi_utils.obj
 0001:00004ca1       _FT_SetBreak               10005ca1 f   ftdi_utils.obj
 0001:00004cd1       _FT_SetChars               10005cd1 f   ftdi_utils.obj
 0001:00004d4c       _FT_SetDeviceEvent         10005d4c f   ftdi_utils.obj
 0001:00004d8b       _FT_SetDtr                 10005d8b f   ftdi_utils.obj
 0001:00004e45       _FT_SetFlowControl         10005e45 f   ftdi_utils.obj
 0001:00004edf       _FT_SetLatencyTimer        10005edf f   ftdi_utils.obj
 0001:00004f82       _FT_SetLineControl         10005f82 f   ftdi_utils.obj
 0001:00004fe5       _FT_SetRts                 10005fe5 f   ftdi_utils.obj
 0001:0000509f       _FT_VendorRequest          1000609f f   ftdi_utils.obj
 0001:0000516b       _IoErrorHandler            1000616b f   ftdi_utils.obj
 0001:000051f4       _LResetPipe                100061f4 f   ftdi_utils.obj
 0001:0000522e       _SoftReset                 1000622e f   ftdi_utils.obj
 0001:000052b7       _ConfigureBulkTransfers    100062b7 f   ftdi_usb.obj
 0001:000053de       _CreateBulkPipeEvents      100063de f   ftdi_usb.obj
 0001:00005430       _CreateUniqueDriverSettings 10006430 f   ftdi_usb.obj
 0001:000058b1       _DelayedDeviceDeactivate   100068b1 f   ftdi_usb.obj
 0001:000058d1       _DeviceNotify              100068d1 f   ftdi_usb.obj
 0001:00005a69       _GetNameIndex              10006a69 f   ftdi_usb.obj
 0001:00005ad1       _GetNextAvailableIndex     10006ad1 f   ftdi_usb.obj
 0001:00005e45       _GetRegistryInitialIndex   10006e45 f   ftdi_usb.obj
 0001:0000601b       _InitialiseDeviceStructure 1000701b f   ftdi_usb.obj
 0001:000061e3       _RemoveDeviceStructure     100071e3 f   ftdi_usb.obj
 0001:000062c4       _RestoreDeviceInstance     100072c4 f   ftdi_usb.obj
 0001:00006435       _RestoreUSBHandles         10007435 f   ftdi_usb.obj
 0001:00006470       _SetIndexKeyValue          10007470 f   ftdi_usb.obj
 0001:00006528       _SetUsbInterface           10007528 f   ftdi_usb.obj
 0001:0000660d       _USBDeviceAttach           1000760d f   ftdi_usb.obj
 0001:00006e84       _USBInstallDriver          10007e84 f   ftdi_usb.obj
 0001:0000706e       _USBUnInstallDriver        1000806e f   ftdi_usb.obj
 0001:00007072       _UpdateDriverVersion       10008072 f   ftdi_usb.obj
 0001:000070d4       _FT_CopyWStrToStr          100080d4 f   STRING.obj
 0001:00007112       _FT_StrCpy                 10008112 f   STRING.obj
 0001:0000712d       _FT_CalcBaudRate           1000812d f   BAUD.obj
 0001:000071b6       _FT_CalcBaudRateHi         100081b6 f   BAUD.obj
 0001:00007248       _FT_CalcDivisor            10008248 f   BAUD.obj
 0001:00007333       _FT_CalcDivisorHi          10008333 f   BAUD.obj
 0001:00007443       _FT_GetDivisor             10008443 f   BAUD.obj
 0001:00007537       _FT_GetDivisorHi           10008537 f   BAUD.obj
 0001:00007624       _BulkInTask                10008624 f   BULK_IN.obj
 0001:00007ab8       _FT_ProcessBulkIn          10008ab8 f   BULK_IN.obj
 0001:00007c34       _FT_ProcessBulkInEx        10008c34 f   BULK_IN.obj
 0001:00007cb2       _InRequest                 10008cb2 f   BULK_IN.obj
 0001:00007d17       ?IsAllBitSet@@YAHPAUSDevice@@E@Z 10008d17 f   usbddrv.obj
 0001:00007d55       ?IsOneBitSet@@YAHPAUSDevice@@E@Z 10008d55 f   usbddrv.obj
 0001:00007d93       ?SetDeviceBit@@YAHPAUSDevice@@EHE@Z 10008d93 f   usbddrv.obj
 0001:00007ddd       ?SignalEventFunc@@YAKPAX@Z 10008ddd f   usbddrv.obj
 0001:00007df2       _AbortPipeTransfers        10008df2 f   usbddrv.obj
 0001:00007e5c       _AbortTransfer             10008e5c f   usbddrv.obj
 0001:00007f6c       _ClearFeature              10008f6c f   usbddrv.obj
 0001:00007fd2       _ClosePipe                 10008fd2 f   usbddrv.obj
 0001:000080db       _CloseTransfer             100090db f   usbddrv.obj
 0001:0000814b       _DisableDevice             1000914b f   usbddrv.obj
 0001:000081ef       _FindInterface             100091ef f   usbddrv.obj
 0001:00008222       _GetClientRegistryPath     10009222 f   usbddrv.obj
 0001:000082b3       _GetDescriptor             100092b3 f   usbddrv.obj
 0001:00008306       _GetDeviceInfo             10009306 f   usbddrv.obj
 0001:00008321       _GetFrameLength            10009321 f   usbddrv.obj
 0001:00008346       _GetFrameNumber            10009346 f   usbddrv.obj
 0001:0000836b       _GetInterface              1000936b f   usbddrv.obj
 0001:000083ab       _GetIsochResults           100093ab f   usbddrv.obj
 0001:00008458       _GetStatus                 10009458 f   usbddrv.obj
 0001:000084c2       _GetTransferStatus         100094c2 f   usbddrv.obj
 0001:00008514       _GetUSBDVersion            10009514 f   usbddrv.obj
 0001:00008526       _IsDefaultPipeHalted       10009526 f   usbddrv.obj
 0001:0000855b       _IsPipeHalted              1000955b f   usbddrv.obj
 0001:0000859b       _IsTransferComplete        1000959b f   usbddrv.obj
 0001:000085be       _IssueInterruptTransfer    100095be f   usbddrv.obj
 0001:000085be       _IssueBulkTransfer         100095be f   usbddrv.obj
 0001:00008727       _IssueControlTransfer      10009727 f   usbddrv.obj
 0001:000088b0       _IssueIsochTransfer        100098b0 f   usbddrv.obj
 0001:00008ab5       _IssueVendorTransfer       10009ab5 f   usbddrv.obj
 0001:00008c9e       _LoadGenericInterfaceDriver 10009c9e f   usbddrv.obj
 0001:00008cc3       _OpenClientRegistryKey     10009cc3 f   usbddrv.obj
 0001:00008d3c       _OpenPipe                  10009d3c f   usbddrv.obj
 0001:00008e13       _RegisterNotificationRoutine 10009e13 f   usbddrv.obj
 0001:00008e6b       _ReleaseFrameLengthControl 10009e6b f   usbddrv.obj
 0001:00008e99       _ResetDefaultPipe          10009e99 f   usbddrv.obj
 0001:00008ecb       _ResetPipe                 10009ecb f   usbddrv.obj
 0001:00008f08       _ResumeDevice              10009f08 f   usbddrv.obj
 0001:00008f6b       _SetDescriptor             10009f6b f   usbddrv.obj
 0001:00008fbe       _SetFeature                10009fbe f   usbddrv.obj
 0001:00009024       _SetFrameLength            1000a024 f   usbddrv.obj
 0001:00009054       _SetInterface              1000a054 f   usbddrv.obj
 0001:00009097       _SuspendDevice             1000a097 f   usbddrv.obj
 0001:0000910f       _SyncFrame                 1000a10f f   usbddrv.obj
 0001:00009155       _TakeFrameLengthControl    1000a155 f   usbddrv.obj
 0001:00009192       _TranslateStringDescr      1000a192 f   usbddrv.obj
 0001:000091d5       _UnRegisterNotificationRoutine 1000a1d5 f   usbddrv.obj
 0001:00009239       ?AddDriverLib@@YAHPAUSDevice@@PAUHINSTANCE__@@@Z 1000a239 f   usbd.obj
 0001:0000928b       ?AddTransfer@@YAHPAUSPipe@@PAUSTransfer@@@Z 1000a28b f   usbd.obj
 0001:00009365       ?ConvertToClientRegistry@@YAHPAGKPBU_USB_DEVICE@@PBU_USB_INTERFACE@@HHHPAU_USB_DRIVER_SETTINGS@@@Z 1000a365 f   usbd.obj
 0001:0000962e       ?DereferencePipeHandle@@YAXPAUSPipe@@@Z 1000a62e f   usbd.obj
 0001:0000962e       ?DereferenceTransferHandle@@YAXPAUSTransfer@@@Z 1000a62e f   usbd.obj
 0001:00009640       ?FreePipeObject@@YAXPAUSPipe@@@Z 1000a640 f   usbd.obj
 0001:0000969e       ?FreeTransferObject@@YAXPAUSTransfer@@@Z 1000a69e f   usbd.obj
 0001:00009798       ?FreeTransferObjectMem@@YAXPAUSTransfer@@@Z 1000a798 f   usbd.obj
 0001:000097d1       ?FreeWaitObject@@YAHPAUSWait@@@Z 1000a7d1 f   usbd.obj
 0001:000097f7       ?GetPipeObject@@YAPAUSPipe@@PAUSDevice@@@Z 1000a7f7 f   usbd.obj
 0001:00009860       ?GetSettingString@@YAXPAGKKKK@Z 1000a860 f   usbd.obj
 0001:000098b8       ?GetTransferObject@@YAPAUSTransfer@@PAUSPipe@@K@Z 1000a8b8 f   usbd.obj
 0001:0000999d       ?GetWaitObject@@YAPAUSWait@@XZ 1000a99d f   usbd.obj
 0001:000099d2       ?LoadGroupDriver@@YAHPAUSDevice@@PAHPBU_USB_INTERFACE@@HHH@Z 1000a9d2 f   usbd.obj
 0001:00009a95       ?LoadRegisteredDriver@@YAHPAUHKEY__@@PAUSDevice@@PBU_USB_INTERFACE@@PAHPBU_USB_DRIVER_SETTINGS@@@Z 1000aa95 f   usbd.obj
 0001:00009c3a       ?LoadUSBClient@@YAHPAUSDevice@@PAHPBU_USB_INTERFACE@@@Z 1000ac3a f   usbd.obj
 0001:00009cc9       ?ReferencePipeHandle@@YAHPAUSPipe@@@Z 1000acc9 f   usbd.obj
 0001:00009d13       ?ReferenceTransferHandle@@YAHPAUSTransfer@@@Z 1000ad13 f   usbd.obj
 0001:00009d5d       ?ValidateDeviceHandle@@YAHPAUSDevice@@@Z 1000ad5d f   usbd.obj
 0001:00009dbb       _CLIClearOrSetFeature      1000adbb f   usbclient.obj
 0001:00009e7b       _CLICloseTransferHandle    1000ae7b f   usbclient.obj
 0001:00009e94       _CLIGetTransferStatus      1000ae94 f   usbclient.obj
 0001:00009ec4       _CLIIssueBulkTransfer      1000aec4 f   usbclient.obj
 0001:00009fc5       _CLIIssueVendorTransfer    1000afc5 f   usbclient.obj
 0001:0000a0ce       _CLIResetDefaultEndpoint   1000b0ce f   usbclient.obj
 0001:0000a11f       _CLIResetPipe              1000b11f f   usbclient.obj
 0001:0000a154       __ResetEvent               1000b154 f i usbclient.obj
 0001:0000a173       ??0MultiPlatformFile@@QAE@XZ 1000b173 f   MultiPlatformFile.obj
 0001:0000a179       _myNOP                     1000b179 f   ftdi_ser.obj
 0001:0000a179       ??1MultiPlatformFile@@QAE@XZ 1000b179 f   MultiPlatformFile.obj
 0001:0000a17a       ?Close@MultiPlatformFile@@QAEHXZ 1000b17a f   MultiPlatformFile.obj
 0001:0000a18a       ?IsOpen@MultiPlatformFile@@QAEHXZ 1000b18a f   MultiPlatformFile.obj
 0001:0000a192       ?Open@MultiPlatformFile@@QAEHPADK@Z 1000b192 f   MultiPlatformFile.obj
 0001:0000a1b8       ?Read@MultiPlatformFile@@QAEHPAXII@Z 1000b1b8 f   MultiPlatformFile.obj
 0001:0000a1f2       ?StringToWchar@@YAXPAGPADK@Z 1000b1f2 f   MultiPlatformFile.obj
 0001:0000a21e       ?FindSection@@YAHPAVMultiPlatformFile@@PBD@Z 1000b21e f   INFParse.obj
 0001:0000a2a4       ?FindVIDPIDInLine@@YAHPAVMultiPlatformFile@@PAK1@Z 1000b2a4 f   INFParse.obj
 0001:0000a419       ?ParseManufacturer@@YAXPAVMultiPlatformFile@@PAK1@Z 1000b419 f   INFParse.obj
 0001:0000a549       ?ParseRegSettings@@YAXPAVMultiPlatformFile@@PAG1@Z 1000b549 f   INFParse.obj
 0001:0000a9af       _GetDevicePrefix           1000b9af f   INFParse.obj
 0001:0000aa30       _GetNextVIDPID             1000ba30 f   INFParse.obj
 0001:0000ab09       _GetStreamDriverKey        1000bb09 f   INFParse.obj
 0001:0000ab20       _RegisterINFValues         1000bb20 f   INFParse.obj
 0001:0000abca       _FT_EmulConvertRxChar      1000bbca f   EMUL.obj
 0001:0000ad01       _FT_EmulCopyTxBytes        1000bd01 f   EMUL.obj
 0001:0000ad63       _FT_EmulGetNextTxByte      1000bd63 f   EMUL.obj
 0001:0000adf0       _FT_EmulProcessLSR         1000bdf0 f   EMUL.obj
 0001:0000ae1c       _FT_EmulProcessMSR         1000be1c f   EMUL.obj
 0001:0000ae54       _FT_EmulProcessRxPacket    1000be54 f   EMUL.obj
 0001:0000b12d       _FT_InitEmulMode           1000c12d f   EMUL.obj
 0001:0000b1ac       _SerialPutChar             1000c1ac f   MODMFLOW.obj
 0001:0000b1e8       _LocalAlloc                1000c1e8 f   coredll:COREDLL.dll
 0001:0000b1ee       _LocalFree                 1000c1ee f   coredll:COREDLL.dll
 0001:0000b1f4       _GetVersionExW             1000c1f4 f   coredll:COREDLL.dll
 0001:0000b1fa       _EnterCriticalSection      1000c1fa f   coredll:COREDLL.dll
 0001:0000b200       _LeaveCriticalSection      1000c200 f   coredll:COREDLL.dll
 0001:0000b206       _InitializeCriticalSection 1000c206 f   coredll:COREDLL.dll
 0001:0000b20c       _DeleteCriticalSection     1000c20c f   coredll:COREDLL.dll
 0001:0000b212       _CreateThread              1000c212 f   coredll:COREDLL.dll
 0001:0000b218       _ExitThread                1000c218 f   coredll:COREDLL.dll
 0001:0000b21e       _CeAllocAsynchronousBuffer 1000c21e f   coredll:COREDLL.dll
 0001:0000b224       _CeFreeAsynchronousBuffer  1000c224 f   coredll:COREDLL.dll
 0001:0000b22a       _EventModify               1000c22a f   coredll:COREDLL.dll
 0001:0000b230       _OpenDeviceKey             1000c230 f   coredll:COREDLL.dll
 0001:0000b236       _TerminateThread           1000c236 f   coredll:COREDLL.dll
 0001:0000b23c       _IsAPIReady                1000c23c f   coredll:COREDLL.dll
 0001:0000b242       _CeEventHasOccurred        1000c242 f   coredll:COREDLL.dll
 0001:0000b248       _wsprintfW                 1000c248 f   coredll:COREDLL.dll
 0001:0000b24e       _LoadLibraryW              1000c24e f   coredll:COREDLL.dll
 0001:0000b254       _NKDbgPrintfW              1000c254 f   coredll:COREDLL.dll
 0001:0000b25a       _ActivateDeviceEx          1000c25a f   coredll:COREDLL.dll
 0001:0000b260       _DeactivateDevice          1000c260 f   coredll:COREDLL.dll
 0001:0000b266       _MessageBoxW               1000c266 f   coredll:COREDLL.dll
 0001:0000b26c       _StringCchCopyW            1000c26c f   coredll:COREDLL.dll
 0001:0000b272       _StringCchCatW             1000c272 f   coredll:COREDLL.dll
 0001:0000b278       _LoadDriver                1000c278 f   coredll:COREDLL.dll
 0001:0000b27e       _StringCchPrintfW          1000c27e f   coredll:COREDLL.dll
 0001:0000b284       @__security_check_cookie@4 1000c284 f   ccrt0:x86secgs.obj
 0001:0000b2ac       __SEH_prolog4              1000c2ac f   ccrt0:sehprolg4.obj
 0001:0000b2f1       __SEH_epilog4              1000c2f1 f   ccrt0:sehprolg4.obj
 0001:0000b305       __except_handler4          1000c305 f   ccrt0:chandler4gs.obj
 0001:0000b328       __SEH_prolog4_GS           1000c328 f   ccrt0:sehprolg4gs.obj
 0001:0000b370       __SEH_epilog4_GS           1000c370 f   ccrt0:sehprolg4gs.obj
 0001:0000b380       _memmove                   1000c380 f   msvcrt:MSVCRT.dll
 0001:0000b386       _memcpy                    1000c386 f   msvcrt:MSVCRT.dll
 0001:0000b38c       _memset                    1000c38c f   msvcrt:MSVCRT.dll
 0001:0000b392       _mbstowcs                  1000c392 f   msvcrt:MSVCRT.dll
 0001:0000b398       __swprintf                 1000c398 f   msvcrt:MSVCRT.dll
 0001:0000b39e       _wcsncmp                   1000c39e f   msvcrt:MSVCRT.dll
 0001:0000b3a4       _wcsncpy                   1000c3a4 f   msvcrt:MSVCRT.dll
 0001:0000b3aa       _swscanf                   1000c3aa f   msvcrt:MSVCRT.dll
 0001:0000b3b0       ??2@YAPAXI@Z               1000c3b0 f   msvcrt:MSVCRT.dll
 0001:0000b3b6       ??3@YAXPAX@Z               1000c3b6 f   msvcrt:MSVCRT.dll
 0001:0000b3bc       _fclose                    1000c3bc f   msvcrt:MSVCRT.dll
 0001:0000b3c2       _ferror                    1000c3c2 f   msvcrt:MSVCRT.dll
 0001:0000b3c8       _fopen                     1000c3c8 f   msvcrt:MSVCRT.dll
 0001:0000b3ce       _fread                     1000c3ce f   msvcrt:MSVCRT.dll
 0001:0000b3d4       _strncmp                   1000c3d4 f   msvcrt:MSVCRT.dll
 0001:0000b3da       _sscanf                    1000c3da f   msvcrt:MSVCRT.dll
 0001:0000b3e0       _sprintf                   1000c3e0 f   msvcrt:MSVCRT.dll
 0001:0000b3e6       __chkstk                   1000c3e6 f   msvcrt:MSVCRT.dll
 0001:0000b3ec       ___report_gsfailure        1000c3ec f   msvcrt:MSVCRT.dll
 0001:0000b3f2       __except_handler4_common   1000c3f2 f   msvcrt:MSVCRT.dll
 0001:0000b544       __IMPORT_DESCRIPTOR_COREDLL 1000c544     coredll:COREDLL.dll
 0001:0000b558       __IMPORT_DESCRIPTOR_MSVCRT 1000c558     msvcrt:MSVCRT.dll
 0001:0000b56c       __NULL_IMPORT_DESCRIPTOR   1000c56c     coredll:COREDLL.dll
 0002:00000000       __imp__LocalAlloc          1000d000     coredll:COREDLL.dll
 0002:00000004       __imp__LocalFree           1000d004     coredll:COREDLL.dll
 0002:00000008       __imp__GetVersionExW       1000d008     coredll:COREDLL.dll
 0002:0000000c       __imp__EnterCriticalSection 1000d00c     coredll:COREDLL.dll
 0002:00000010       __imp__LeaveCriticalSection 1000d010     coredll:COREDLL.dll
 0002:00000014       __imp__InitializeCriticalSection 1000d014     coredll:COREDLL.dll
 0002:00000018       __imp__DeleteCriticalSection 1000d018     coredll:COREDLL.dll
 0002:0000001c       __imp__CreateThread        1000d01c     coredll:COREDLL.dll
 0002:00000020       __imp__ExitThread          1000d020     coredll:COREDLL.dll
 0002:00000024       __imp__GetTickCount        1000d024     coredll:COREDLL.dll
 0002:00000028       __imp__CloseHandle         1000d028     coredll:COREDLL.dll
 0002:0000002c       __imp__SetThreadPriority   1000d02c     coredll:COREDLL.dll
 0002:00000030       __imp__CeGetThreadPriority 1000d030     coredll:COREDLL.dll
 0002:00000034       __imp__CeSetThreadPriority 1000d034     coredll:COREDLL.dll
 0002:00000038       __imp__SetLastError        1000d038     coredll:COREDLL.dll
 0002:0000003c       __imp__Sleep               1000d03c     coredll:COREDLL.dll
 0002:00000040       __imp__CreateEventW        1000d040     coredll:COREDLL.dll
 0002:00000044       __imp__WaitForSingleObject 1000d044     coredll:COREDLL.dll
 0002:00000048       __imp__DisableThreadLibraryCalls 1000d048     coredll:COREDLL.dll
 0002:0000004c       __imp__CeAllocAsynchronousBuffer 1000d04c     coredll:COREDLL.dll
 0002:00000050       __imp__CeFreeAsynchronousBuffer 1000d050     coredll:COREDLL.dll
 0002:00000054       __imp__EventModify         1000d054     coredll:COREDLL.dll
 0002:00000058       __imp__RegCloseKey         1000d058     coredll:COREDLL.dll
 0002:0000005c       __imp__RegQueryValueExW    1000d05c     coredll:COREDLL.dll
 0002:00000060       __imp__OpenDeviceKey       1000d060     coredll:COREDLL.dll
 0002:00000064       __imp__TerminateThread     1000d064     coredll:COREDLL.dll
 0002:00000068       __imp__GetLastError        1000d068     coredll:COREDLL.dll
 0002:0000006c       __imp__GetThreadPriority   1000d06c     coredll:COREDLL.dll
 0002:00000070       __imp__CeSetThreadQuantum  1000d070     coredll:COREDLL.dll
 0002:00000074       __imp__CreateMutexW        1000d074     coredll:COREDLL.dll
 0002:00000078       __imp__ReleaseMutex        1000d078     coredll:COREDLL.dll
 0002:0000007c       __imp__IsAPIReady          1000d07c     coredll:COREDLL.dll
 0002:00000080       __imp__RegOpenKeyExW       1000d080     coredll:COREDLL.dll
 0002:00000084       __imp__RegSetValueExW      1000d084     coredll:COREDLL.dll
 0002:00000088       __imp__CeEventHasOccurred  1000d088     coredll:COREDLL.dll
 0002:0000008c       __imp__GetProcAddressW     1000d08c     coredll:COREDLL.dll
 0002:00000090       __imp__wsprintfW           1000d090     coredll:COREDLL.dll
 0002:00000094       __imp__LoadLibraryW        1000d094     coredll:COREDLL.dll
 0002:00000098       __imp__FreeLibrary         1000d098     coredll:COREDLL.dll
 0002:0000009c       __imp__NKDbgPrintfW        1000d09c     coredll:COREDLL.dll
 0002:000000a0       __imp__ActivateDeviceEx    1000d0a0     coredll:COREDLL.dll
 0002:000000a4       __imp__DeactivateDevice    1000d0a4     coredll:COREDLL.dll
 0002:000000a8       __imp__MessageBoxW         1000d0a8     coredll:COREDLL.dll
 0002:000000ac       __imp__RegCreateKeyExW     1000d0ac     coredll:COREDLL.dll
 0002:000000b0       __imp__RegDeleteKeyW       1000d0b0     coredll:COREDLL.dll
 0002:000000b4       __imp__RegEnumKeyExW       1000d0b4     coredll:COREDLL.dll
 0002:000000b8       __imp__WaitForMultipleObjects 1000d0b8     coredll:COREDLL.dll
 0002:000000bc       __imp__StringCchCopyW      1000d0bc     coredll:COREDLL.dll
 0002:000000c0       __imp__StringCchCatW       1000d0c0     coredll:COREDLL.dll
 0002:000000c4       __imp__LoadDriver          1000d0c4     coredll:COREDLL.dll
 0002:000000c8       __imp__StringCchPrintfW    1000d0c8     coredll:COREDLL.dll
 0002:000000cc       \177COREDLL_NULL_THUNK_DATA 1000d0cc     coredll:COREDLL.dll
 0002:000000d0       __imp__memmove             1000d0d0     msvcrt:MSVCRT.dll
 0002:000000d4       __imp__memcpy              1000d0d4     msvcrt:MSVCRT.dll
 0002:000000d8       __imp__memset              1000d0d8     msvcrt:MSVCRT.dll
 0002:000000dc       __imp__mbstowcs            1000d0dc     msvcrt:MSVCRT.dll
 0002:000000e0       __imp___swprintf           1000d0e0     msvcrt:MSVCRT.dll
 0002:000000e4       __imp__wcsncmp             1000d0e4     msvcrt:MSVCRT.dll
 0002:000000e8       __imp__wcsncpy             1000d0e8     msvcrt:MSVCRT.dll
 0002:000000ec       __imp__swscanf             1000d0ec     msvcrt:MSVCRT.dll
 0002:000000f0       __imp_??2@YAPAXI@Z         1000d0f0     msvcrt:MSVCRT.dll
 0002:000000f4       __imp_??3@YAXPAX@Z         1000d0f4     msvcrt:MSVCRT.dll
 0002:000000f8       __imp__fclose              1000d0f8     msvcrt:MSVCRT.dll
 0002:000000fc       __imp__ferror              1000d0fc     msvcrt:MSVCRT.dll
 0002:00000100       __imp__fopen               1000d100     msvcrt:MSVCRT.dll
 0002:00000104       __imp__fread               1000d104     msvcrt:MSVCRT.dll
 0002:00000108       __imp__strncmp             1000d108     msvcrt:MSVCRT.dll
 0002:0000010c       __imp__sscanf              1000d10c     msvcrt:MSVCRT.dll
 0002:00000110       __imp__sprintf             1000d110     msvcrt:MSVCRT.dll
 0002:00000114       __imp___chkstk             1000d114     msvcrt:MSVCRT.dll
 0002:00000118       __imp____report_gsfailure  1000d118     msvcrt:MSVCRT.dll
 0002:0000011c       __imp___except_handler4_common 1000d11c     msvcrt:MSVCRT.dll
 0002:00000120       \177MSVCRT_NULL_THUNK_DATA 1000d120     msvcrt:MSVCRT.dll
 0002:00000158       ?gcszUsbConfigureEntry@@3PBGB 1000d158     usbddrv.obj
 0002:00000160       ?gcszUsbRegKey@@3PBGB      1000d160     usbd.obj
 0002:00000164       ?gcszDriverIDs@@3PBGB      1000d164     usbd.obj
 0002:00000168       ?gcszLoadClients@@3PBGB    1000d168     usbd.obj
 0002:0000016c       ?gcszDllName@@3PBGB        1000d16c     usbd.obj
 0002:00000170       ?gcszDefaultDriver@@3PBGB  1000d170     usbd.obj
 0002:00000174       ?gcszUSBDeviceAttach@@3PBGB 1000d174     usbd.obj
 0002:00000178       ?gcszUSBInstallDriver@@3PBGB 1000d178     usbd.obj
 0002:00000230       ___security_cookie         1000d230     ccrt0:seccinit.obj
 0002:00000234       ___security_cookie_complement 1000d234     ccrt0:seccinit.obj
 0002:00000240       _gbAttached                1000d240     mdd.obj
 0002:00000660       _gszStreamDriverKey        1000d660     <common>
 0002:00000880       _osv                       1000d880     <common>

 entry point at        0001:00001f83

 Static symbols

 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     msvcrt:MSVCRT.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0000:ffff0000       .debug$S                   10000000     coredll:COREDLL.dll
 0001:00000988       _acSections                10001988     INFParse.obj
 0001:00001580       $LN134                     10002580     mdd.obj
 0001:000015e0       $LN102                     100025e0     mdd.obj
 0001:00002249       _SerialDispatchThread      10003249 f   mdd.obj
 0001:0000269b       _SerInit                   1000369b f   ftdi_ser.obj
 0001:000027de       _SerPostInit               100037de f   ftdi_ser.obj
 0001:000027ef       _SerDeinit                 100037ef f   ftdi_ser.obj
 0001:00002892       _SerOpen                   10003892 f   ftdi_ser.obj
 0001:000028c6       _SerClose                  100038c6 f   ftdi_ser.obj
 0001:00003542       _SerGetCommProperties      10004542 f   ftdi_ser.obj
 0001:000037f6       _FTDIEventThread           100047f6 f   ftdi_ser.obj
 0001:0000706e       _SerPowerOn                1000806e f   ftdi_usb.obj
 0001:0000706e       _SerPowerOff               1000806e f   ftdi_usb.obj
 0001:0000706e       _SerEnableIR               1000806e f   ftdi_usb.obj
 0001:0000706e       _SerDisableIR              1000806e f   ftdi_usb.obj
 0001:0000b3f8       __sehtable$_SerRxIntr      1000c3f8     ftdi_ser.obj
 0001:0000b418       __sehtable$_SerPurgeComm   1000c418     ftdi_ser.obj
 0001:0000b438       __sehtable$_SerXmitComChar 1000c438     ftdi_ser.obj
 0001:0000b458       __sehtable$_SerIoctl       1000c458     ftdi_ser.obj
 0001:0000b4c8       __sehtable$?ReferencePipeHandle@@YAHPAUSPipe@@@Z 1000c4c8     usbd.obj
 0001:0000b4e8       __sehtable$?ValidateDeviceHandle@@YAHPAUSDevice@@@Z 1000c4e8     usbd.obj
 0001:0000b508       __sehtable$?ReferenceTransferHandle@@YAHPAUSTransfer@@@Z 1000c508     usbd.obj
 0001:0000b528       __sehtable$?LoadRegisteredDriver@@YAHPAUHKEY__@@PAUSDevice@@PBU_USB_INTERFACE@@PAHPBU_USB_DRIVER_SETTINGS@@@Z 1000c528     usbd.obj
 0001:0000b6a4       .idata$6                   1000c6a4     coredll:COREDLL.dll
 0001:0000b6b0       .idata$6                   1000c6b0     msvcrt:MSVCRT.dll
 0001:0000b6c0       .edata                     1000c6c0     ftdi_ser.exp
 0001:0000b6e8       rgpv                       1000c6e8     ftdi_ser.exp
 0001:0000b71c       rgszName                   1000c71c     ftdi_ser.exp
 0001:0000b750       rgwOrd                     1000c750     ftdi_ser.exp
 0001:0000b76a       szName                     1000c76a     ftdi_ser.exp
 0001:0000b777       $N00001                    1000c777     ftdi_ser.exp
 0001:0000b781       $N00002                    1000c781     ftdi_ser.exp
 0001:0000b78c       $N00003                    1000c78c     ftdi_ser.exp
 0001:0000b79a       $N00004                    1000c79a     ftdi_ser.exp
 0001:0000b7a3       $N00005                    1000c7a3     ftdi_ser.exp
 0001:0000b7ac       $N00006                    1000c7ac     ftdi_ser.exp
 0001:0000b7ba       $N00007                    1000c7ba     ftdi_ser.exp
 0001:0000b7c6       $N00008                    1000c7c6     ftdi_ser.exp
 0001:0000b7cf       $N00009                    1000c7cf     ftdi_ser.exp
 0001:0000b7d8       $N00010                    1000c7d8     ftdi_ser.exp
 0001:0000b7e2       $N00011                    1000c7e2     ftdi_ser.exp
 0001:0000b7f2       $N00012                    1000c7f2     ftdi_ser.exp
 0001:0000b803       $N00013                    1000c803     ftdi_ser.exp
 0002:00000128       _g_DriverSettings          1000d128     ftdi_usb.obj
 0002:00000150       _gszDriverPrefix           1000d150     ftdi_usb.obj
 0002:00000180       _gc_UsbFuncs               1000d180     usbd.obj
 0002:00000244       _GlobalSerialHeadNumber    1000d244     mdd.obj
 0002:00000248       _GlobalSerialHeadNumber    1000d248     ftdi_ser.obj
 0002:0000024c       _GlobalSerialHeadNumber    1000d24c     ftdi_usb.obj
 0002:00000250       _gcPortName                1000d250     INFParse.obj
 0002:00000650       ?pmpf@?1??GetNextVIDPID@@9@4PAVMultiPlatformFile@@A 1000d650     INFParse.obj
 0003:00000060       $R000000                   1000e060     ftdi_ser.res
