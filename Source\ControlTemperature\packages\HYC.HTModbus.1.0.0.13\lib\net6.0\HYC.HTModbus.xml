<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HYC.HTModbus</name>
    </assembly>
    <members>
        <member name="T:HYC.HTModbus.FunctionCode">
            <summary>
            Modbus功能码
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.ReadCoils">
            <summary>
            读线圈
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.ReadInputs">
            <summary>
            读离散输入
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.ReadHoldingRegisters">
            <summary>
            读保持寄存器
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.ReadInputRegisters">
            <summary>
            读输入寄存器
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.WriteSingleCoil">
            <summary>
            写单个线圈
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.WriteSingleRegister">
            <summary>
            写单个保持寄存器
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.WriteMultipleCoils">
            <summary>
            写多个线圈
            </summary>
        </member>
        <member name="F:HYC.HTModbus.FunctionCode.WriteMultipleRegisters">
            <summary>
            写多个保持寄存器
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusAsciiMaster.#ctor(HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo)">
            <summary>
            构造方法
            </summary>
            <param name="info"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusAsciiMaster.Client_DataReceived(System.Object,HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs)">
            <summary>
            接收到数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusAsciiMaster.Connect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusAsciiMaster.Read(System.Byte,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusAsciiMaster.Write(System.Byte,System.Byte,System.UInt16,System.UInt16,System.Byte[])">
            <summary>
            写数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <param name="data">数据</param>
        </member>
        <member name="T:HYC.HTModbus.ModbusExtentions">
            <summary>
            Modbus拓展方法
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读short类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">short数据的数量</param>
            <returns>short数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Int16[]@)">
            <summary>
            读short类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">short数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int16[])">
            <summary>
            写ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ushort数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int16[])">
            <summary>
            写ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ushort数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadUInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">ushort数据的数量</param>
            <returns>ushort数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadUInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.UInt16[]@)">
            <summary>
            读ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">ushort数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteUInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16[])">
            <summary>
            写ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ushort数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteUInt16(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16[])">
            <summary>
            写ushort类型的数据
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ushort数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读int类型的数据，每个int占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">int数据的数量</param>
            <returns>int数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Int32[]@)">
            <summary>
            读int类型的数据，每个int占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">int数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int32[])">
            <summary>
            写int类型的数据，每个int占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">int数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int32[])">
            <summary>
            写int类型的数据，每个int占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">int数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadUInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读uint类型的数据，每个uint占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">uint数据的数量</param>
            <returns>uint数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadUInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.UInt32[]@)">
            <summary>
            读uint类型的数据，每个uint占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">uint数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteUInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt32[])">
            <summary>
            写uint类型的数据，每个uint占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">uint数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteUInt32(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt32[])">
            <summary>
            写uint类型的数据，每个uint占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">uint数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读long类型的数据，每个long占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">long数据的数量</param>
            <returns>long数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Int64[]@)">
            <summary>
            读long类型的数据，每个long占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">long数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int64[])">
            <summary>
            写long类型的数据，每个long占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">long数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Int64[])">
            <summary>
            写long类型的数据，每个long占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">long数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadUInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读ulong类型的数据，每个ulong占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">ulong数据的数量</param>
            <returns>ulong数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadUInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.UInt64[]@)">
            <summary>
            读ulong类型的数据，每个ulong占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">ulong数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteUInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt64[])">
            <summary>
            写ulong类型的数据，每个ulong占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ulong数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteUInt64(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt64[])">
            <summary>
            写ulong类型的数据，每个ulong占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">ulong数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadFloat(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读float类型的数据，每个float占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">float数据的数量</param>
            <returns>float数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadFloat(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Single[]@)">
            <summary>
            读float类型的数据，每个float占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">float数据的数量</param>
            <param name="datas">读取的数量</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteFloat(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Single[])">
            <summary>
            写float类型的数据，每个float占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">float数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteFloat(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Single[])">
            <summary>
            写float类型的数据，每个float占用2个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">float数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadDouble(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读double类型的数据，每个double占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">double数据的数量</param>
            <returns>double数组</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadDouble(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Double[]@)">
            <summary>
            读double类型的数据，每个double占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="count">double数据的数量</param>
            <param name="datas">读取的数据</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteDouble(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Double[])">
            <summary>
            写double类型的数据，每个double占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">double数组</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteDouble(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.Double[])">
            <summary>
            写double类型的数据，每个double占用4个地址
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="datas">double数组</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ReadString(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.Boolean)">
            <summary>
            读字符串
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="length">字符串长度</param>
            <param name="isReverse">是否反转字符 ABCD->BADC</param>
            <returns>字符串</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryReadString(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.UInt16,System.String@,System.Boolean)">
            <summary>
            读字符串
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="length">字符串长度</param>
            <param name="str">读取的字符串</param>
            <param name="isReverse">是否反转字符 ABCD->BADC</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.WriteString(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.String,System.Boolean)">
            <summary>
            写字符串
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="str">字符串</param>
            <param name="isReverse">是否反转字符 ABCD->BADC</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.TryWriteString(HYC.HTModbus.ModbusMaster,System.Byte,System.UInt16,System.String,System.Boolean)">
            <summary>
            写字符串
            </summary>
            <param name="master">ModbusMaster对象</param>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="str">字符串</param>
            <param name="isReverse">是否反转字符 ABCD->BADC</param>
            <returns>true：成功；false：失败</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.Int16[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.UInt16[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.Int32[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.UInt32[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.Int64[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.UInt64[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.Single[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToBytes(System.Double[],System.Boolean)">
            <summary>
            数据转换为byte数组
            </summary>
            <param name="buffer"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToInt16(System.Byte[],System.Boolean)">
            <summary>
            数据转换为short数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToUInt16(System.Byte[],System.Boolean)">
            <summary>
            数据转换为ushort数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToInt32(System.Byte[],System.Boolean)">
            <summary>
            数据转换为int数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToUInt32(System.Byte[],System.Boolean)">
            <summary>
            数据转换为uint数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToInt64(System.Byte[],System.Boolean)">
            <summary>
            数据转换为long数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToUInt64(System.Byte[],System.Boolean)">
            <summary>
            数据转换为ulong数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToSingle(System.Byte[],System.Boolean)">
            <summary>
            数据转换为float数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusExtentions.ToDouble(System.Byte[],System.Boolean)">
            <summary>
            数据转换为double数组
            </summary>
            <param name="bytes"></param>
            <param name="isBigEndian"></param>
            <returns></returns>
        </member>
        <member name="T:HYC.HTModbus.ModbusMaster">
            <summary>
            Modbus Master抽象类
            </summary>
        </member>
        <member name="P:HYC.HTModbus.ModbusMaster.TimeOut">
            <summary>
            通信超时时间，默认值1000ms
            </summary>
        </member>
        <member name="P:HYC.HTModbus.ModbusMaster.IsBigEndian">
            <summary>
            是否为Big Endian模式
            </summary>
        </member>
        <member name="P:HYC.HTModbus.ModbusMaster.IsConnected">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:HYC.HTModbus.ModbusMaster.IsCheckCRC">
            <summary>
            是否检查CRC，默认为true
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.CreateTCP(HYC.HTCommunication.SocketWrapper.HTSocketInfo)">
            <summary>
            创建ModbusTCP实例
            </summary>
            <param name="info">Socket连接信息</param>
            <returns>ModbusMaster对象</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.CreateRTU(HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo)">
            <summary>
            创建ModbusRTU实例
            </summary>
            <param name="info">串口连接信息</param>
            <returns>ModbusMaster对象</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.CreateASCII(HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo)">
            <summary>
            创建ModbusASCII实例
            </summary>
            <param name="info">串口连接信息</param>
            <returns>ModbusMaster对象</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.ReadCoils(System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读线圈
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>读取的数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.WriteMultipleCoils(System.Byte,System.UInt16,System.Boolean[])">
            <summary>
            写多个线圈
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="data">写入的数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.WriteSingleCoil(System.Byte,System.UInt16,System.Boolean)">
            <summary>
            写单个线圈
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="data">写入的数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.ReadHoldingRegisters(System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读取保持寄存器
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>读取的数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.WriteMultipleRegisters(System.Byte,System.UInt16,System.UInt16[])">
            <summary>
            写保持寄存器
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="data">写入的数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.WriteSingleRegister(System.Byte,System.UInt16,System.UInt16)">
            <summary>
            写单个寄存器
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="data">写入的数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.ReadInputs(System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读离散输入
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>读取的数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.ReadInputRegisters(System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读输入寄存器
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>读取的数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.Connect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.Read(System.Byte,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.Write(System.Byte,System.Byte,System.UInt16,System.UInt16,System.Byte[])">
            <summary>
            写数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <param name="data">数据</param>
        </member>
        <member name="M:HYC.HTModbus.ModbusMaster.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusRtuMaster.#ctor(HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo)">
            <summary>
            构造方法
            </summary>
            <param name="info"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusRtuMaster.Client_DataReceived(System.Object,HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs)">
            <summary>
            接收到数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusRtuMaster.Connect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusRtuMaster.Read(System.Byte,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusRtuMaster.Write(System.Byte,System.Byte,System.UInt16,System.UInt16,System.Byte[])">
            <summary>
            写数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <param name="data">数据</param>
        </member>
        <member name="F:HYC.HTModbus.ModbusTcpMaster.dataIndex">
            <summary>
            数据序号标识
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.#ctor(HYC.HTCommunication.SocketWrapper.HTSocketInfo)">
            <summary>
            构造方法
            </summary>
            <param name="info"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.Connect">
            <summary>
            连接
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.Client_ClientConnected(System.Object,HYC.HTCommunication.SocketWrapper.SocketEventArgs)">
            <summary>
            连接成功
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.Client_ClientDisconnected(System.Object,HYC.HTCommunication.SocketWrapper.SocketEventArgs)">
            <summary>
            连接断开
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.socketClicent_DataReceived(System.Object,HYC.HTCommunication.SocketWrapper.SocketEventArgs)">
            <summary>
            接收到数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.Read(System.Byte,System.Byte,System.UInt16,System.UInt16)">
            <summary>
            读数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <returns>数据</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusTcpMaster.Write(System.Byte,System.Byte,System.UInt16,System.UInt16,System.Byte[])">
            <summary>
            写数据抽象方法
            </summary>
            <param name="slaveAddress">节点地址</param>
            <param name="functionCode">功能码</param>
            <param name="startingAddress">起始地址</param>
            <param name="registerNumber">寄存器数量</param>
            <param name="data">数据</param>
        </member>
        <member name="T:HYC.HTModbus.ModbusUtility">
            <summary>
            Modbus utility methods.
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.GetSingle(System.UInt16,System.UInt16)">
            <summary>
            Converts two UInt16 values into a IEEE 32 floating point format
            </summary>
            <param name="highOrderValue">High order ushort value</param>
            <param name="lowOrderValue">Low order ushort value</param>
            <returns>IEEE 32 floating point value</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.GetUInt32(System.UInt16,System.UInt16)">
            <summary>
            Converts two UInt16 values into a UInt32
            </summary>
            <param name="highOrderValue"></param>
            <param name="lowOrderValue"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.GetAsciiBytes(System.Byte[])">
            <summary>
            Converts an array of bytes to an ASCII byte array
            </summary>
            <param name="numbers">The byte array</param>
            <returns>An array of ASCII byte values</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.GetAsciiBytes(System.UInt16[])">
            <summary>
            Converts an array of UInt16 to an ASCII byte array
            </summary>
            <param name="numbers">The ushort array</param>
            <returns>An array of ASCII byte values</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.NetworkBytesToHostUInt16(System.Byte[])">
            <summary>
            Converts a network order byte array to an array of UInt16 values in host order
            </summary>
            <param name="networkBytes">The network order byte array</param>
            <returns>The host order ushort array</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.HexToBytes(System.String)">
            <summary>
            Converts a hex string to a byte array.
            </summary>
            <param name="hex">The hex string</param>
            <returns>Array of bytes</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.CalculateLrc(System.Byte[])">
            <summary>
            Calculate Longitudinal Redundancy Check.
            </summary>
            <param name="data">The data used in LRC</param>
            <returns>LRC value</returns>
        </member>
        <member name="M:HYC.HTModbus.ModbusUtility.CalculateCrc(System.Byte[])">
            <summary>
            Calculate Cyclical Redundancy Check
            </summary>
            <param name="data">The data used in CRC</param>
            <returns>CRC value</returns>
        </member>
        <member name="T:HYC.HTModbus.ValueHelper">
            <summary>
            基本数据转换帮助类
            </summary>
        </member>
        <member name="P:HYC.HTModbus.ValueHelper.Instance">
            <summary>
            实例
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.Int16)">
            <summary>
            以字节数组的形式返回指定的 16 位有符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字</param>
            <returns>长度为 2 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.UInt16)">
            <summary>
            以字节数组的形式返回指定的 16 位无符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 2 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.Int32)">
            <summary>
            以字节数组的形式返回指定的 32 位有符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 4 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.UInt32)">
            <summary>
            以字节数组的形式返回指定的 32 位无符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 4 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.Int64)">
            <summary>
            以字节数组的形式返回指定的 64 位有符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 8 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.UInt64)">
            <summary>
            以字节数组的形式返回指定的 64 位无符号整数值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 8 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.Single)">
            <summary>
            以字节数组的形式返回指定的单精度浮点值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 4 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.GetBytes(System.Double)">
            <summary>
            以字节数组的形式返回指定的双精度浮点值。BigEndian，高位在前，低位在后。
            </summary>
            <param name="value">要转换的数字。</param>
            <returns>长度为 8 的字节数组。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToInt16(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的两个字节转换来的 16 位有符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由两个字节构成、从 startIndex 开始的 16 位有符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToUInt16(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的两个字节转换来的 16 位无符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由两个字节构成、从 startIndex 开始的 16 位无符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToInt32(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的四个字节转换来的 32 位有符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由四个字节构成、从 startIndex 开始的 32 位有符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToUInt32(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的四个字节转换来的 32 位无符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由四个字节构成、从 startIndex 开始的 32 位无符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToInt64(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的八个字节转换来的 64 位有符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由四个字节构成、从 startIndex 开始的 32 位有符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToUInt64(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的八个字节转换来的 64 位无符号整数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data">字节数组。</param>
            <param name="startIndex">data内的起始位置。</param>
            <returns>由四个字节构成、从 startIndex 开始的 32 位无符号整数。</returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToSingle(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的四个字节转换来的 32 位单精度浮点数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data"></param>
            <param name="startIndex"></param>
            <returns></returns>
        </member>
        <member name="M:HYC.HTModbus.ValueHelper.ToDouble(System.Byte[],System.Int32)">
            <summary>
            返回由字节数组中指定位置的八个字节转换来的 64 位双精度浮点数。输入数组为BigEndian，高位在前，低位在后。
            </summary>
            <param name="data"></param>
            <param name="startIndex"></param>
            <returns></returns>
        </member>
    </members>
</doc>
