; FTDIBUS.INF
; Copyright (c) 2000-2004 FTDI Ltd.

[Version]
signature="$CHICAGO$"
Class=USB
ClassGUID={36fc9e60-c465-11cf-8056-************}
Provider=AVRVi
CatalogFile=ftdibus.cat
DriverVer=04/16/2004,1.00.2154

[SourceDisksNames]
1=%DriversDisk%,,,

[SourceDisksFiles]
ftcomms.vxd = 1
ftserial.sys = 1
ftsenum.vxd = 1
ftsenum.sys = 1
ftsermou.vxd = 1
ftserui.dll = 1
ftlang.dll = 1
ftdibus.sys = 1
ftdiunin.exe = 1
ftdiunin.ini = 1
ftdiun2k.ini = 1

[DestinationDirs]
FtdiBus.Copy1 = 11
FtdiBus.Copy2 = 17
FtdiBus.NT.Copy = 10,system32\drivers
FtdiBus.NT.Copy2 = 11

[Manufacturer]
%Ftdi%=FtdiHw

[FtdiHw]
%USB\VID_0403&PID_8372.DeviceDesc%=FtdiBus,USB\VID_0403&PID_8372
%USB\VID_0403&PID_6001.DeviceDesc%=FtdiBus,USB\VID_0403&PID_6001
%USB\VID_0403&PID_6007.DeviceDesc%=FtdiBus,USB\VID_0403&PID_6007
%USB\VID_0403&PID_6008.DeviceDesc%=FtdiBus,USB\VID_0403&PID_6008
%USB\VID_0403&PID_6009.DeviceDesc%=FtdiBus,USB\VID_0403&PID_6009
%USB\VID_0403&PID_0232.DeviceDesc%=FtdiBus,USB\VID_0403&PID_0232

[ControlFlags]
ExcludeFromSelect=USB\VID_0403&PID_8372
ExcludeFromSelect=USB\VID_0403&PID_6001
ExcludeFromSelect=USB\VID_0403&PID_6007
ExcludeFromSelect=USB\VID_0403&PID_6008
ExcludeFromSelect=USB\VID_0403&PID_6009
ExcludeFromSelect=USB\VID_0403&PID_0232

[FtdiBus]
CopyFiles=FtdiBus.Copy1,FtdiBus.Copy2
AddReg=FtdiBus.AddReg, FtdiBusUnInst.Reg
RemoveDevice=FtdiBus.RemDev

[FtdiBus.HW]
AddReg=FtdiBus.AddReg.HW

[FtdiBus.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,ftserial.sys

[FtdiBus.AddReg.HW]
HKR,,"UpperFilters",0x00010000,"ftsenum.sys"

[FtdiBus.Copy1]
ftcomms.vxd
ftsenum.vxd
ftsermou.vxd
ftserial.sys
ftsenum.sys
ftserui.dll
ftdiunin.exe
ftdiunin.ini

[FtdiBus.Copy2]
ftdibus.inf
ftdiport.inf
ftsermou.inf

[FtdiBus.RemDev]
DelFiles=FtdiBus.Delfiles
DELKEY=HKLM,%WINUN%\FTDICOMM

[FtdiBusUnInst.Reg]
HKLM,%WINUN%,"FTDICOMM"
HKLM,%WINUN%\FTDICOMM , "UninstallString",,"%11%\ftdiunin.exe %11%\ftdiunin.ini"
HKLM,%WINUN%\FTDICOMM , "DisplayName",,"AVRVi MKii"

[FtdiBus.DelFiles]
FTCOMMS.VXD
ftsenum.vxd
ftsermou.vxd
FTSERIAL.SYS
FTPORTS.INF
FTDICOM.INF
ftdibus.inf
ftdiport.inf
ftsenum.sys
ftserui.dll
ftdiunin.exe
ftdiunin.ini

;---------------------------------------------------------------;
; For Win2000 ...
;

[FtdiBus.NT]
CopyFiles=FtdiBus.NT.Copy,FtdiBus.NT.Copy2
AddReg=FtdiBus.NT.AddReg,FtdiBusUnInst.NT.Reg

[FtdiBus.NT.Services]
AddService = FTDIBUS, 0x00000002, FtdiBus_AddService

[FtdiBus_AddService]
DisplayName    = %FtdiBus.SvcDesc%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %10%\system32\drivers\ftdibus.sys
LoadOrderGroup = Base
AddReg         = FtdiBus_AddService.AddReg

[FtdiBus.NT.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,ftdibus.sys

[FtdiBus_AddService.AddReg]
HKR,Parameters,"MaximumTransferSize",0x10001,4096
;HKR,Parameters,"DebugLevel",0x10001,2

[FtdiBus.NT.Copy]
ftdibus.sys

[FtdiBus.NT.Copy2]
ftdiunin.exe
ftdiun2k.ini

[FtdiBusUnInst.NT.Reg]
HKLM,%WINUN%,"FTDICOMM"
HKLM,%WINUN%\FTDICOMM , "UninstallString",,"%11%\ftdiunin.exe %11%\ftdiun2k.ini"
HKLM,%WINUN%\FTDICOMM , "DisplayName",,"AVRVi MKii"

;---------------------------------------------------------------;

[Strings]
Ftdi="AVRVi"
DriversDisk="AVRVi MKii Drivers Disk"
USB\VID_0403&PID_8372.DeviceDesc="AVRVi MKii"
USB\VID_0403&PID_6001.DeviceDesc="AVRVi MKii"
USB\VID_0403&PID_6007.DeviceDesc="AVRVi MKii"
USB\VID_0403&PID_6008.DeviceDesc="AVRVi MKii"
USB\VID_0403&PID_6009.DeviceDesc="AVRVi MKii"
USB\VID_0403&PID_0232.DeviceDesc="AVRVi MKii"
WINUN="Software\Microsoft\Windows\CurrentVersion\Uninstall"
FtdiBus.SvcDesc="AVRVi MKii"
