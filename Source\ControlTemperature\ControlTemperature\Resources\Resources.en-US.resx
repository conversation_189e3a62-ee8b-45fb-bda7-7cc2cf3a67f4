<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Main Window -->
  <data name="MainWindow_Title" xml:space="preserve">
    <value>Temperature Control Platform System</value>
  </data>
  <data name="MainWindow_ConnectDevice" xml:space="preserve">
    <value>Connect Device</value>
  </data>
  <data name="MainWindow_DisconnectDevice" xml:space="preserve">
    <value>Disconnect Device</value>
  </data>
  <data name="MainWindow_ConnectionStatus" xml:space="preserve">
    <value>Connection Status:</value>
  </data>
  <data name="MainWindow_ActiveDevices" xml:space="preserve">
    <value>Active Devices:</value>
  </data>
  <data name="MainWindow_TcpServer" xml:space="preserve">
    <value>TCP Server:</value>
  </data>
  <data name="MainWindow_SystemStatus" xml:space="preserve">
    <value>System Status: Running Normally</value>
  </data>
  <data name="MainWindow_CommunicationStatus" xml:space="preserve">
    <value>Communication Status: Normal</value>
  </data>
  <data name="MainWindow_LastUpdate" xml:space="preserve">
    <value>Last Update:</value>
  </data>
  <data name="MainWindow_Language" xml:space="preserve">
    <value>Language:</value>
  </data>
  
  <!-- Controller Control -->
  <data name="Controller_SerialPort" xml:space="preserve">
    <value>Serial Port:</value>
  </data>
  <data name="Controller_DeviceStatus" xml:space="preserve">
    <value>Device Status:</value>
  </data>
  <data name="Controller_CommunicationStatus" xml:space="preserve">
    <value>Communication Status:</value>
  </data>
  <data name="Controller_Header" xml:space="preserve">
    <value>Controller{0} (Address:{1})</value>
  </data>
  
  <!-- Channel Control -->
  <data name="Channel_Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Channel_EmergencyStop" xml:space="preserve">
    <value>E-Stop</value>
  </data>
  <data name="Channel_CurrentTemperature" xml:space="preserve">
    <value>Current Temperature</value>
  </data>
  <data name="Channel_TargetTemperature" xml:space="preserve">
    <value>Target Temperature</value>
  </data>
  <data name="Channel_TemperatureSettings" xml:space="preserve">
    <value>Temperature Settings</value>
  </data>
  <data name="Channel_SetTemperature" xml:space="preserve">
    <value>Set Temperature:</value>
  </data>
  <data name="Channel_MaxTemperature" xml:space="preserve">
    <value>Max Temperature:</value>
  </data>
  <data name="Channel_MinTemperature" xml:space="preserve">
    <value>Min Temperature:</value>
  </data>
  <data name="Channel_SetButton" xml:space="preserve">
    <value>Set</value>
  </data>
  <data name="Channel_OutputStatus" xml:space="preserve">
    <value>Output Status</value>
  </data>
  <data name="Channel_RunningMode" xml:space="preserve">
    <value>Running Mode:</value>
  </data>
  <data name="Channel_FanStatus" xml:space="preserve">
    <value>Fan Status:</value>
  </data>
  <data name="Channel_RealtimeLog" xml:space="preserve">
    <value>Realtime Log</value>
  </data>
  <data name="Channel_ClearLog" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Channel_SettingEnableStatus" xml:space="preserve">
    <value>Setting enable status...</value>
  </data>
  <data name="Channel_SettingTemperature" xml:space="preserve">
    <value>Setting temperature...</value>
  </data>
  
  <!-- Status Text -->
  <data name="Status_Connected" xml:space="preserve">
    <value>Connected</value>
  </data>
  <data name="Status_Disconnected" xml:space="preserve">
    <value>Disconnected</value>
  </data>
  <data name="Status_Online" xml:space="preserve">
    <value>Online</value>
  </data>
  <data name="Status_Offline" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="Status_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="Status_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="Status_NotStarted" xml:space="preserve">
    <value>Not Started</value>
  </data>
  <data name="Status_Heating" xml:space="preserve">
    <value>Heating</value>
  </data>
  <data name="Status_Cooling" xml:space="preserve">
    <value>Cooling</value>
  </data>
  <data name="Status_Running" xml:space="preserve">
    <value>Running</value>
  </data>
  <data name="Status_Stopped" xml:space="preserve">
    <value>Stopped</value>
  </data>
  <data name="Status_Enabled" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="Status_Disabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  
  <!-- Language Options -->
  <data name="Language_Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Language_English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Language_Korean" xml:space="preserve">
    <value>한국어</value>
  </data>

  <!-- Log Messages -->
  <data name="Log_SystemStartup" xml:space="preserve">
    <value>System starting...</value>
  </data>
  <data name="Log_SystemInitialized" xml:space="preserve">
    <value>Log system initialized</value>
  </data>
  <data name="Log_ChannelInitialized" xml:space="preserve">
    <value>Channel {0} initialized, port: {1}</value>
  </data>
  <data name="Log_ChannelConfigLoadFailed" xml:space="preserve">
    <value>Channel {0} configuration load failed</value>
  </data>
  <data name="Log_DataUpdateFailed" xml:space="preserve">
    <value>Data update failed: {0}</value>
  </data>
  <data name="Log_EnableStatusSet" xml:space="preserve">
    <value>Enable status set to: {0}</value>
  </data>
  <data name="Log_EnableStatusSetFailed" xml:space="preserve">
    <value>Set enable status failed: {0}</value>
  </data>
  <data name="Log_DeviceNotConnected" xml:space="preserve">
    <value>Device not connected, cannot set temperature</value>
  </data>
  <data name="Log_TemperatureSettingComplete" xml:space="preserve">
    <value>Temperature setting complete</value>
  </data>
  <data name="Log_TemperatureSettingFailed" xml:space="preserve">
    <value>Temperature setting failed: {0}</value>
  </data>
  <data name="Log_TargetTemperatureSet" xml:space="preserve">
    <value>Target temperature set to: {0}℃</value>
  </data>
  <data name="Log_MaxTemperatureSet" xml:space="preserve">
    <value>Max temperature set to: {0}℃</value>
  </data>
  <data name="Log_MinTemperatureSet" xml:space="preserve">
    <value>Min temperature set to: {0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSet" xml:space="preserve">
    <value>Ambient temperature set to: {0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>Set ambient temperature failed: {0}</value>
  </data>
  <data name="Log_EmergencyStopExecuted" xml:space="preserve">
    <value>Emergency stop executed, enable disabled</value>
  </data>
  <data name="Log_EmergencyStopFailed" xml:space="preserve">
    <value>Emergency stop failed: {0}</value>
  </data>
  <data name="Log_LogCleared" xml:space="preserve">
    <value>Log cleared</value>
  </data>

  <!-- Error Messages -->
  <data name="Error_SetTargetTemperatureFailed" xml:space="preserve">
    <value>Set target temperature failed</value>
  </data>
  <data name="Error_SetMaxTemperatureFailed" xml:space="preserve">
    <value>Set max temperature failed</value>
  </data>
  <data name="Error_SetMinTemperatureFailed" xml:space="preserve">
    <value>Set min temperature failed</value>
  </data>
  <data name="Error_SetChannelEnableFailed" xml:space="preserve">
    <value>Set channel {0} enable status failed</value>
  </data>
  <data name="Error_SetAmbientTemperatureFailed" xml:space="preserve">
    <value>Set ambient temperature failed</value>
  </data>
  <data name="Error_UIThreadCallFailed" xml:space="preserve">
    <value>UI thread call failed: {0}</value>
  </data>
  <data name="Error_LogRecordFailed" xml:space="preserve">
    <value>Log record failed: {0}</value>
  </data>
  <data name="Error_UpdateLogEntryFailed" xml:space="preserve">
    <value>Update log entry failed: {0}</value>
  </data>

  <!-- TCP Server Messages -->
  <data name="Tcp_ServerStarted" xml:space="preserve">
    <value>TCP server started, listening on port: {0}</value>
  </data>
  <data name="Tcp_ServerStartFailed" xml:space="preserve">
    <value>Start TCP server failed: {0}</value>
  </data>
  <data name="Tcp_ServerStopped" xml:space="preserve">
    <value>TCP server stopped</value>
  </data>
  <data name="Tcp_ServerStopFailed" xml:space="preserve">
    <value>Stop TCP server failed: {0}</value>
  </data>
  <data name="Tcp_ClientConnected" xml:space="preserve">
    <value>Client connected: {0}</value>
  </data>
  <data name="Tcp_ClientConnectionFailed" xml:space="preserve">
    <value>Accept client connection failed: {0}</value>
  </data>
  <data name="Tcp_MessageReceived" xml:space="preserve">
    <value>Message received from {0}: {1}</value>
  </data>
  <data name="Tcp_ProcessMessageFailed" xml:space="preserve">
    <value>Process client {0} message failed: {1}</value>
  </data>
  <data name="Tcp_ClientDisconnected" xml:space="preserve">
    <value>Client {0} disconnected</value>
  </data>
  <data name="Tcp_ResponseSent" xml:space="preserve">
    <value>Response sent: {0}</value>
  </data>
  <data name="Tcp_ProcessMessageFailedGeneral" xml:space="preserve">
    <value>Process message failed: {0}</value>
  </data>
  <data name="Tcp_ErrorResponseSent" xml:space="preserve">
    <value>Error response sent: {0}</value>
  </data>
  <data name="Tcp_ControllerOutOfRange" xml:space="preserve">
    <value>Controller number out of range (1-8)</value>
  </data>
  <data name="Tcp_ControllerNotConnected" xml:space="preserve">
    <value>Controller {0} not connected</value>
  </data>
  <data name="Tcp_UnknownCommandType" xml:space="preserve">
    <value>Unknown command type: {0}</value>
  </data>
  <data name="Tcp_ProcessCommandFailed" xml:space="preserve">
    <value>Process command failed: {0}</value>
  </data>
  <data name="Tcp_ChannelOutOfRange" xml:space="preserve">
    <value>Channel number out of range (1-2)</value>
  </data>
  <data name="Tcp_MissingTemperatureValue" xml:space="preserve">
    <value>Missing temperature value parameter</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetSuccess" xml:space="preserve">
    <value>Ambient temperature set successfully</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>Ambient temperature set failed</value>
  </data>
  <data name="Tcp_TargetTemperatureSetSuccess" xml:space="preserve">
    <value>Target temperature set successfully</value>
  </data>
  <data name="Tcp_TargetTemperatureSetFailed" xml:space="preserve">
    <value>Target temperature set failed</value>
  </data>
  <data name="Tcp_MissingEnableValue" xml:space="preserve">
    <value>Missing enable status parameter</value>
  </data>
  <data name="Tcp_TecEnabled" xml:space="preserve">
    <value>TEC enabled</value>
  </data>
  <data name="Tcp_TecDisabled" xml:space="preserve">
    <value>TEC disabled</value>
  </data>
  <data name="Tcp_TecEnableSetFailed" xml:space="preserve">
    <value>TEC enable set failed</value>
  </data>
  <data name="Tcp_MaxTemperatureSetSuccess" xml:space="preserve">
    <value>Max temperature set successfully</value>
  </data>
  <data name="Tcp_MaxTemperatureSetFailed" xml:space="preserve">
    <value>Max temperature set failed</value>
  </data>
  <data name="Tcp_MinTemperatureSetSuccess" xml:space="preserve">
    <value>Min temperature set successfully</value>
  </data>
  <data name="Tcp_MinTemperatureSetFailed" xml:space="preserve">
    <value>Min temperature set failed</value>
  </data>
  <data name="Tcp_ReadTemperatureDataFailed" xml:space="preserve">
    <value>Read temperature data failed</value>
  </data>
  <data name="Tcp_ReadStatusFailed" xml:space="preserve">
    <value>Read status failed: {0}</value>
  </data>
</root>
