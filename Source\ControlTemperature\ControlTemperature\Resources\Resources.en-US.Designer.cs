//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ControlTemperature.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit the .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources_en_US {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources_en_US() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ControlTemperature.Resources.Resources.en-US", typeof(Resources_en_US).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable.
        /// </summary>
        internal static string Channel_Enable {
            get {
                return ResourceManager.GetString("Channel_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-Stop.
        /// </summary>
        internal static string Channel_EmergencyStop {
            get {
                return ResourceManager.GetString("Channel_EmergencyStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Temperature.
        /// </summary>
        internal static string Channel_CurrentTemperature {
            get {
                return ResourceManager.GetString("Channel_CurrentTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Temperature.
        /// </summary>
        internal static string Channel_TargetTemperature {
            get {
                return ResourceManager.GetString("Channel_TargetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temperature Settings.
        /// </summary>
        internal static string Channel_TemperatureSettings {
            get {
                return ResourceManager.GetString("Channel_TemperatureSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set Temperature:.
        /// </summary>
        internal static string Channel_SetTemperature {
            get {
                return ResourceManager.GetString("Channel_SetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Temperature:.
        /// </summary>
        internal static string Channel_MaxTemperature {
            get {
                return ResourceManager.GetString("Channel_MaxTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min Temperature:.
        /// </summary>
        internal static string Channel_MinTemperature {
            get {
                return ResourceManager.GetString("Channel_MinTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set.
        /// </summary>
        internal static string Channel_SetButton {
            get {
                return ResourceManager.GetString("Channel_SetButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Output Status.
        /// </summary>
        internal static string Channel_OutputStatus {
            get {
                return ResourceManager.GetString("Channel_OutputStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Running Mode:.
        /// </summary>
        internal static string Channel_RunningMode {
            get {
                return ResourceManager.GetString("Channel_RunningMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fan Status:.
        /// </summary>
        internal static string Channel_FanStatus {
            get {
                return ResourceManager.GetString("Channel_FanStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realtime Log.
        /// </summary>
        internal static string Channel_RealtimeLog {
            get {
                return ResourceManager.GetString("Channel_RealtimeLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        internal static string Channel_ClearLog {
            get {
                return ResourceManager.GetString("Channel_ClearLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temperature Control Platform System.
        /// </summary>
        internal static string MainWindow_Title {
            get {
                return ResourceManager.GetString("MainWindow_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect Device.
        /// </summary>
        internal static string MainWindow_ConnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_ConnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect Device.
        /// </summary>
        internal static string MainWindow_DisconnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_DisconnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection Status:.
        /// </summary>
        internal static string MainWindow_ConnectionStatus {
            get {
                return ResourceManager.GetString("MainWindow_ConnectionStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Devices:.
        /// </summary>
        internal static string MainWindow_ActiveDevices {
            get {
                return ResourceManager.GetString("MainWindow_ActiveDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TCP Server:.
        /// </summary>
        internal static string MainWindow_TcpServer {
            get {
                return ResourceManager.GetString("MainWindow_TcpServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language:.
        /// </summary>
        internal static string MainWindow_Language {
            get {
                return ResourceManager.GetString("MainWindow_Language", resourceCulture);
            }
        }
    }
}
