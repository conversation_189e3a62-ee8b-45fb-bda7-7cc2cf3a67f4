﻿<UserControl x:Class="ControlTemperature.View.ChannelControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ControlTemperature.View"
             xmlns:vm="clr-namespace:ControlTemperature.ViewModel"
             xmlns:helpers="clr-namespace:ControlTemperature.Helper"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 转换器 -->
        <helpers:BoolToHeatingModeConverter x:Key="BoolToHeatingModeConverter"/>
        <helpers:BoolToFanStatusConverter x:Key="BoolToFanStatusConverter"/>
        <helpers:BoolToInverseBoolConverter x:Key="BoolToInverseBoolConverter"/>
        
        <!-- 样式 -->
        <Style TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Height" Value="25"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style TargetType="Button">
            <Setter Property="Height" Value="28"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="Padding" Value="8,3"/>
        </Style>

        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

    </UserControl.Resources>

    <Border BorderBrush="#DDDDDD" BorderThickness="2" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 通道标题 -->
            <!--第一行-->
            <Border Grid.Row="0" Background="{Binding HeaderColor}" CornerRadius="5,5,0,0"
                    Padding="10" Margin="0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!--第一列-->
                    <TextBlock Grid.Column="0" Text="{Binding ChannelName}" FontWeight="Bold"
                               FontSize="18" Foreground="White" HorizontalAlignment="Center"/>
                    <!--第二列-->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="CH" FontWeight="Bold" FontSize="15" Foreground="White"/>
                        <TextBlock Text="{Binding ChannelNumber}" FontWeight="Bold" FontSize="15"
                                   Foreground="White"/>
                    </StackPanel>
                </Grid>
            </Border>


            <!-- 使能和急停控制 -->
            <!--第二行-->
            <Border Grid.Row="1" Background="#F9F9F9" Padding="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <!--第一列-->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,15,0">
                        <CheckBox Content="{Binding EnableText}" FontWeight="Bold" FontSize="15"
                                  VerticalAlignment="Center" Margin="0,0,10,0"
                                  IsChecked="{Binding IsEnabled}"
                                  IsEnabled="{Binding IsEnableOperationInProgress, Converter={StaticResource BoolToInverseBoolConverter}}"/>

                        <!-- 忙状态指示器 -->
                        <TextBlock Text="⏳" FontSize="14" VerticalAlignment="Center" ToolTip="{Binding SettingEnableStatusText}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsEnableOperationInProgress}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>

                    <!--第三列-->
                    <Button Grid.Column="2" Content="{Binding EmergencyStopText}" Background="#F44336" Foreground="White"
                           FontWeight="Bold" Width="50" Command="{Binding EmergencyStopCommand}"/>
                </Grid>
            </Border>

            <!--温度显示-->
            <!--第三行-->
            <Grid Grid.Row="2" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!--第一列-->
                <Border Grid.Column="0" Background="#E8F5E8" BorderBrush="#4CAF50"
                       BorderThickness="2" CornerRadius="5" Padding="10">
                    <StackPanel>
                        <TextBlock Text="{Binding CurrentTemperatureText}" HorizontalAlignment="Center" FontWeight="Bold"
                                  FontSize="12" Foreground="#333"/>
                        <TextBlock Text="{Binding CurrentTemperature, StringFormat={}{0:F3}°C}"
                                  HorizontalAlignment="Center" FontWeight="Bold"
                                  FontSize="24" Foreground="#4CAF50" Margin="0,5"/>
                    </StackPanel>
                </Border>

                <!--第三列-->
                <Border Grid.Column="2" Background="#FFF3E0" BorderBrush="#FF9800"
                       BorderThickness="2" CornerRadius="5" Padding="10">
                    <StackPanel>
                        <TextBlock Text="{Binding TargetTemperatureText}" HorizontalAlignment="Center" FontWeight="Bold"
                                  FontSize="12" Foreground="#333"/>
                        <TextBlock Text="{Binding TargetTemperature, StringFormat={}{0:F3}°C}"
                                  HorizontalAlignment="Center" FontWeight="Bold"
                                  FontSize="24" Foreground="#FF9800" Margin="0,5"/>
                    </StackPanel>
                </Border>

            </Grid>

            <!-- 温度设置 -->
            <!--第四行-->
            <GroupBox Grid.Row="3" Header="{Binding TemperatureSettingsText}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding SetTemperatureText}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBox Grid.Column="1" Text="{Binding TargetTemperatureSetting, StringFormat={}{0:F3}, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Column="2" Text="°C" VerticalAlignment="Center" Margin="2,0,10,0"/>

                    <TextBlock Grid.Column="3" Text="{Binding MaxTemperatureText}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBox Grid.Column="4" Text="{Binding MaxTemperatureSetting, StringFormat={}{0:F3}, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Column="5" Text="°C" VerticalAlignment="Center" Margin="2,0,10,0"/>

                    <TextBlock Grid.Column="6" Text="{Binding MinTemperatureText}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBox Grid.Column="7" Text="{Binding MinTemperatureSetting, StringFormat={}{0:F3}, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center"/>
                    <TextBlock Grid.Column="8" Text="°C" VerticalAlignment="Center" Margin="2,0,10,0"/>

                    <StackPanel Grid.Column="10" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Content="{Binding SetButtonText}" Background="#2196F3"
                               Foreground="White" Width="60" Height="30" VerticalAlignment="Center"
                               Command="{Binding ApplyTemperatureSettingsCommand}"
                               IsEnabled="{Binding IsTemperatureSettingInProgress, Converter={StaticResource BoolToInverseBoolConverter}}"/>

                        <!-- 忙状态指示器 -->
                        <TextBlock Text="⏳" FontSize="14" VerticalAlignment="Center" Margin="5,0,0,0" ToolTip="{Binding SettingTemperatureText}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsTemperatureSettingInProgress}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- 详细信息标签页 -->
            <!--第五行-->
            <TabControl Grid.Row="4" Margin="5">
                <TabItem Header="{Binding OutputStatusText}">
                    <Grid Margin="5">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Border Grid.Row="2" Background="#E3F2FD" BorderBrush="#2196F3"
                               BorderThickness="1" CornerRadius="3" Padding="8" Margin="0,0,0,3">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="{Binding RunningModeText}" FontWeight="Bold"/>
                                <TextBlock Grid.Column="1" Text="{Binding IsHeating, Converter={StaticResource BoolToHeatingModeConverter}}"
                                          FontWeight="Bold" Foreground="#2196F3" FontSize="14"/>
                            </Grid>
                        </Border>

                        <Border Grid.Row="3" Background="#FFF3E0" BorderBrush="#FF9800"
                               BorderThickness="1" CornerRadius="3" Padding="8" Margin="0,0,0,3">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="{Binding FanStatusText}" FontWeight="Bold"/>
                                <TextBlock Grid.Column="1" Text="{Binding FanRunning, Converter={StaticResource BoolToFanStatusConverter}}"
                                          FontWeight="Bold" Foreground="#FF9800" FontSize="14"/>
                            </Grid>
                        </Border>

                        <!-- 实时日志显示区域 -->
                        <Border Grid.Row="4" Background="#F8F9FA" BorderBrush="#DEE2E6"
                               BorderThickness="1" CornerRadius="3" Padding="0" Margin="0,0,0,0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- 日志标题和清空按钮 -->
                                <Border Grid.Row="0" Background="#E9ECEF" BorderBrush="#DEE2E6"
                                       BorderThickness="0,0,0,1" Padding="5,3">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="{Binding RealtimeLogText}" FontWeight="Bold" FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Button Grid.Column="1" Content="{Binding ClearLogText}" Width="45" Height="20"
                                               FontSize="9" Background="#FF5722" Foreground="White"
                                               Command="{Binding ClearLogCommand}"/>
                                    </Grid>
                                </Border>
                                
                                <!-- 日志内容显示区域 -->
                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" 
                                            HorizontalScrollBarVisibility="Auto" Padding="0"
                                            VirtualizingPanel.IsVirtualizing="True" 
                                            VirtualizingPanel.VirtualizationMode="Recycling">
                                    <TextBlock Text="{Binding LogMessages}" FontFamily="Consolas" 
                                              FontSize="11" Background="White" Padding="6,4"
                                              TextWrapping="Wrap" VerticalAlignment="Top"
                                              LineHeight="14" LineStackingStrategy="BlockLineHeight"/>
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </Grid>
                </TabItem>

                <!--<TabItem Header="PID参数" Visibility="Collapsed">
                    <ScrollViewer>
                        <StackPanel Margin="10">
                            -->
                <!-- 制冷PID -->
                <!--
                            <GroupBox Header="制冷PID参数" Margin="0,0,0,15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="P (比例):"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="1.50"/>
                                    <Button Grid.Row="0" Grid.Column="2" Content="设置" Width="40"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="I (积分):"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="0.20"/>
                                    <Button Grid.Row="1" Grid.Column="2" Content="设置" Width="40"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="D (微分):"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" Text="0.05"/>
                                    <Button Grid.Row="2" Grid.Column="2" Content="设置" Width="40"/>
                                </Grid>
                            </GroupBox>

                            -->
                <!-- 加热PID -->
                <!--
                            <GroupBox Header="加热PID参数" Margin="0,0,0,15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="P (比例):"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="1.20"/>
                                    <Button Grid.Row="0" Grid.Column="2" Content="设置" Width="40"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="I (积分):"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="0.15"/>
                                    <Button Grid.Row="1" Grid.Column="2" Content="设置" Width="40"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="D (微分):"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" Text="0.03"/>
                                    <Button Grid.Row="2" Grid.Column="2" Content="设置" Width="40"/>
                                </Grid>
                            </GroupBox>

                            -->
                <!-- 操作按钮 -->
                <!--
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Button Grid.Column="0" Content="读取参数" Background="#2196F3" 
                                       Foreground="White" Height="35" Margin="0,0,5,0"/>
                                <Button Grid.Column="1" Content="应用参数" Background="#4CAF50" 
                                       Foreground="White" Height="35" Margin="5,0"/>
                                <Button Grid.Column="2" Content="保存参数" Background="#FF9800" 
                                       Foreground="White" Height="35" Margin="5,0,0,0"/>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>-->
            </TabControl>
        </Grid>
    </Border>



</UserControl>
