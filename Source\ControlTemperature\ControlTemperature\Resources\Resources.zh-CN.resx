<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- 主窗口 -->
  <data name="MainWindow_Title" xml:space="preserve">
    <value>温控平台控制系统</value>
  </data>
  <data name="MainWindow_ConnectDevice" xml:space="preserve">
    <value>连接设备</value>
  </data>
  <data name="MainWindow_DisconnectDevice" xml:space="preserve">
    <value>断开连接</value>
  </data>
  <data name="MainWindow_ConnectionStatus" xml:space="preserve">
    <value>连接状态:</value>
  </data>
  <data name="MainWindow_ActiveDevices" xml:space="preserve">
    <value>活动设备:</value>
  </data>
  <data name="MainWindow_TcpServer" xml:space="preserve">
    <value>TCP服务器:</value>
  </data>
  <data name="MainWindow_SystemStatus" xml:space="preserve">
    <value>系统状态: 正常运行</value>
  </data>
  <data name="MainWindow_CommunicationStatus" xml:space="preserve">
    <value>通讯状态: 正常</value>
  </data>
  <data name="MainWindow_LastUpdate" xml:space="preserve">
    <value>最后更新:</value>
  </data>
  <data name="MainWindow_Language" xml:space="preserve">
    <value>语言:</value>
  </data>
  
  <!-- 控制器控件 -->
  <data name="Controller_SerialPort" xml:space="preserve">
    <value>串口号:</value>
  </data>
  <data name="Controller_DeviceStatus" xml:space="preserve">
    <value>设备状态:</value>
  </data>
  <data name="Controller_CommunicationStatus" xml:space="preserve">
    <value>通讯状态:</value>
  </data>
  <data name="Controller_Header" xml:space="preserve">
    <value>控制器{0} (地址:{1})</value>
  </data>
  
  <!-- 通道控件 -->
  <data name="Channel_Enable" xml:space="preserve">
    <value>使能</value>
  </data>
  <data name="Channel_EmergencyStop" xml:space="preserve">
    <value>急停</value>
  </data>
  <data name="Channel_CurrentTemperature" xml:space="preserve">
    <value>当前温度</value>
  </data>
  <data name="Channel_TargetTemperature" xml:space="preserve">
    <value>目标温度</value>
  </data>
  <data name="Channel_TemperatureSettings" xml:space="preserve">
    <value>温度设置</value>
  </data>
  <data name="Channel_SetTemperature" xml:space="preserve">
    <value>设置温度:</value>
  </data>
  <data name="Channel_MaxTemperature" xml:space="preserve">
    <value>上限温度:</value>
  </data>
  <data name="Channel_MinTemperature" xml:space="preserve">
    <value>下限温度:</value>
  </data>
  <data name="Channel_SetButton" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="Channel_OutputStatus" xml:space="preserve">
    <value>输出状态</value>
  </data>
  <data name="Channel_RunningMode" xml:space="preserve">
    <value>运行模式:</value>
  </data>
  <data name="Channel_FanStatus" xml:space="preserve">
    <value>风扇状态:</value>
  </data>
  <data name="Channel_RealtimeLog" xml:space="preserve">
    <value>实时日志</value>
  </data>
  <data name="Channel_ClearLog" xml:space="preserve">
    <value>清空</value>
  </data>
  <data name="Channel_SettingEnableStatus" xml:space="preserve">
    <value>正在设置使能状态...</value>
  </data>
  <data name="Channel_SettingTemperature" xml:space="preserve">
    <value>正在设置温度...</value>
  </data>
  
  <!-- 状态文本 -->
  <data name="Status_Connected" xml:space="preserve">
    <value>已连接</value>
  </data>
  <data name="Status_Disconnected" xml:space="preserve">
    <value>未连接</value>
  </data>
  <data name="Status_Online" xml:space="preserve">
    <value>在线</value>
  </data>
  <data name="Status_Offline" xml:space="preserve">
    <value>离线</value>
  </data>
  <data name="Status_Normal" xml:space="preserve">
    <value>正常</value>
  </data>
  <data name="Status_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="Status_NotStarted" xml:space="preserve">
    <value>未启动</value>
  </data>
  <data name="Status_Heating" xml:space="preserve">
    <value>加热</value>
  </data>
  <data name="Status_Cooling" xml:space="preserve">
    <value>制冷</value>
  </data>
  <data name="Status_Running" xml:space="preserve">
    <value>运行</value>
  </data>
  <data name="Status_Stopped" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="Status_Enabled" xml:space="preserve">
    <value>开启</value>
  </data>
  <data name="Status_Disabled" xml:space="preserve">
    <value>关闭</value>
  </data>
  
  <!-- 语言选项 -->
  <data name="Language_Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Language_English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Language_Korean" xml:space="preserve">
    <value>한국어</value>
  </data>

  <!-- 日志消息 -->
  <data name="Log_SystemStartup" xml:space="preserve">
    <value>系统启动...</value>
  </data>
  <data name="Log_SystemInitialized" xml:space="preserve">
    <value>日志系统已初始化</value>
  </data>
  <data name="Log_ChannelInitialized" xml:space="preserve">
    <value>通道 {0} 初始化完成，端口：{1}</value>
  </data>
  <data name="Log_ChannelConfigLoadFailed" xml:space="preserve">
    <value>通道 {0} 配置加载失败</value>
  </data>
  <data name="Log_DataUpdateFailed" xml:space="preserve">
    <value>数据更新失败：{0}</value>
  </data>
  <data name="Log_EnableStatusSet" xml:space="preserve">
    <value>使能状态设置为：{0}</value>
  </data>
  <data name="Log_EnableStatusSetFailed" xml:space="preserve">
    <value>设置使能状态异常：{0}</value>
  </data>
  <data name="Log_DeviceNotConnected" xml:space="preserve">
    <value>设备未连接，无法设置温度</value>
  </data>
  <data name="Log_TemperatureSettingComplete" xml:space="preserve">
    <value>温度设置完成</value>
  </data>
  <data name="Log_TemperatureSettingFailed" xml:space="preserve">
    <value>温度设置异常：{0}</value>
  </data>
  <data name="Log_TargetTemperatureSet" xml:space="preserve">
    <value>目标温度设置为：{0}℃</value>
  </data>
  <data name="Log_MaxTemperatureSet" xml:space="preserve">
    <value>温度上限设置为：{0}℃</value>
  </data>
  <data name="Log_MinTemperatureSet" xml:space="preserve">
    <value>温度下限设置为：{0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSet" xml:space="preserve">
    <value>环境温度设置为：{0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>设置环境温度异常：{0}</value>
  </data>
  <data name="Log_EmergencyStopExecuted" xml:space="preserve">
    <value>急停操作执行，已关闭使能</value>
  </data>
  <data name="Log_EmergencyStopFailed" xml:space="preserve">
    <value>急停操作异常：{0}</value>
  </data>
  <data name="Log_LogCleared" xml:space="preserve">
    <value>日志已清空</value>
  </data>

  <!-- 错误消息 -->
  <data name="Error_SetTargetTemperatureFailed" xml:space="preserve">
    <value>设置目标温度失败</value>
  </data>
  <data name="Error_SetMaxTemperatureFailed" xml:space="preserve">
    <value>设置温度上限失败</value>
  </data>
  <data name="Error_SetMinTemperatureFailed" xml:space="preserve">
    <value>设置温度下限失败</value>
  </data>
  <data name="Error_SetChannelEnableFailed" xml:space="preserve">
    <value>设置通道 {0} 使能状态失败</value>
  </data>
  <data name="Error_SetAmbientTemperatureFailed" xml:space="preserve">
    <value>设置环境温度失败</value>
  </data>
  <data name="Error_UIThreadCallFailed" xml:space="preserve">
    <value>UI线程调用失败: {0}</value>
  </data>
  <data name="Error_LogRecordFailed" xml:space="preserve">
    <value>日志记录失败: {0}</value>
  </data>
  <data name="Error_UpdateLogEntryFailed" xml:space="preserve">
    <value>更新日志条目失败: {0}</value>
  </data>

  <!-- TCP服务器消息 -->
  <data name="Tcp_ServerStarted" xml:space="preserve">
    <value>TCP服务器已启动，监听端口：{0}</value>
  </data>
  <data name="Tcp_ServerStartFailed" xml:space="preserve">
    <value>启动TCP服务器失败：{0}</value>
  </data>
  <data name="Tcp_ServerStopped" xml:space="preserve">
    <value>TCP服务器已停止</value>
  </data>
  <data name="Tcp_ServerStopFailed" xml:space="preserve">
    <value>停止TCP服务器失败：{0}</value>
  </data>
  <data name="Tcp_ClientConnected" xml:space="preserve">
    <value>客户端已连接：{0}</value>
  </data>
  <data name="Tcp_ClientConnectionFailed" xml:space="preserve">
    <value>接受客户端连接失败：{0}</value>
  </data>
  <data name="Tcp_MessageReceived" xml:space="preserve">
    <value>收到来自 {0} 的消息：{1}</value>
  </data>
  <data name="Tcp_ProcessMessageFailed" xml:space="preserve">
    <value>处理客户端 {0} 消息失败：{1}</value>
  </data>
  <data name="Tcp_ClientDisconnected" xml:space="preserve">
    <value>客户端 {0} 已断开连接</value>
  </data>
  <data name="Tcp_ResponseSent" xml:space="preserve">
    <value>已发送响应：{0}</value>
  </data>
  <data name="Tcp_ProcessMessageFailedGeneral" xml:space="preserve">
    <value>处理消息失败：{0}</value>
  </data>
  <data name="Tcp_ErrorResponseSent" xml:space="preserve">
    <value>发送错误响应：{0}</value>
  </data>
  <data name="Tcp_ControllerOutOfRange" xml:space="preserve">
    <value>控制器编号超出范围（1-8）</value>
  </data>
  <data name="Tcp_ControllerNotConnected" xml:space="preserve">
    <value>控制器 {0} 未连接</value>
  </data>
  <data name="Tcp_UnknownCommandType" xml:space="preserve">
    <value>未知的命令类型：{0}</value>
  </data>
  <data name="Tcp_ProcessCommandFailed" xml:space="preserve">
    <value>处理命令失败：{0}</value>
  </data>
  <data name="Tcp_ChannelOutOfRange" xml:space="preserve">
    <value>通道编号超出范围（1-2）</value>
  </data>
  <data name="Tcp_MissingTemperatureValue" xml:space="preserve">
    <value>缺少温度值参数</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetSuccess" xml:space="preserve">
    <value>环境温度设置成功</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>环境温度设置失败</value>
  </data>
  <data name="Tcp_TargetTemperatureSetSuccess" xml:space="preserve">
    <value>目标温度设置成功</value>
  </data>
  <data name="Tcp_TargetTemperatureSetFailed" xml:space="preserve">
    <value>目标温度设置失败</value>
  </data>
  <data name="Tcp_MissingEnableValue" xml:space="preserve">
    <value>缺少使能状态参数</value>
  </data>
  <data name="Tcp_TecEnabled" xml:space="preserve">
    <value>TEC已开启</value>
  </data>
  <data name="Tcp_TecDisabled" xml:space="preserve">
    <value>TEC已关闭</value>
  </data>
  <data name="Tcp_TecEnableSetFailed" xml:space="preserve">
    <value>TEC使能设置失败</value>
  </data>
  <data name="Tcp_MaxTemperatureSetSuccess" xml:space="preserve">
    <value>最大温度设置成功</value>
  </data>
  <data name="Tcp_MaxTemperatureSetFailed" xml:space="preserve">
    <value>最大温度设置失败</value>
  </data>
  <data name="Tcp_MinTemperatureSetSuccess" xml:space="preserve">
    <value>最小温度设置成功</value>
  </data>
  <data name="Tcp_MinTemperatureSetFailed" xml:space="preserve">
    <value>最小温度设置失败</value>
  </data>
  <data name="Tcp_ReadTemperatureDataFailed" xml:space="preserve">
    <value>读取温度数据失败</value>
  </data>
  <data name="Tcp_ReadStatusFailed" xml:space="preserve">
    <value>读取状态失败：{0}</value>
  </data>
</root>
