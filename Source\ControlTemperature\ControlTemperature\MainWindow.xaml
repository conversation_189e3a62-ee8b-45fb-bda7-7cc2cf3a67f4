﻿<Window x:Class="ControlTemperature.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ControlTemperature"
        xmlns:views="clr-namespace:ControlTemperature.View"
        xmlns:helpers="clr-namespace:ControlTemperature.Helper"
        mc:Ignorable="d"
        Title="温控平台控制系统" Height="900" Width="1400"
        WindowState="Maximized">
    <Window.Resources>
        <!-- 转换器 -->
        <helpers:BoolToConnectionStatusConverter x:Key="BoolToConnectionStatusConverter"/>
        <helpers:BoolToColorConverter x:Key="BoolToColorConverter"/>
        
        <!-- 样式 -->
        <Style TargetType="TabItem">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="2,0"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <Grid Margin="15,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="连接设备" Width="80" Height="30" Margin="0,0,5,0" 
                            Command="{Binding ConnectCommand}"/>
                    <Button Content="断开连接" Width="80" Height="30" Margin="5,0" 
                            Command="{Binding DisconnectCommand}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="连接状态:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding IsConnected, Converter={StaticResource BoolToConnectionStatusConverter}}" 
                               VerticalAlignment="Center" 
                               Foreground="{Binding IsConnected, Converter={StaticResource BoolToColorConverter}}" 
                               FontWeight="Bold" Margin="0,0,20,0"/>
                    <TextBlock Text="活动设备:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding ControllerCount}" VerticalAlignment="Center" Foreground="Blue" FontWeight="Bold" Margin="0,0,20,0"/>
                    <TextBlock Text="TCP服务器:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding TcpServerStatus}" VerticalAlignment="Center" 
                               Foreground="{Binding IsTcpServerRunning, Converter={StaticResource BoolToColorConverter}}" 
                               FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 控制器标签页 -->
        <TabControl Grid.Row="1" Margin="10" ItemsSource="{Binding Controllers}" SelectedIndex="{Binding SelectedControllerIndex}">
            <TabControl.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding ControllerHeader}"/>
                </DataTemplate>
            </TabControl.ItemTemplate>
            <TabControl.ContentTemplate>
                <DataTemplate>
                    <views:ControllerControl DataContext="{Binding}"/>
                </DataTemplate>
            </TabControl.ContentTemplate>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" Margin="15,5">
                <TextBlock Text="系统状态: 正常运行" Margin="0,0,20,0"/>
                <TextBlock Text="{Binding LastUpdateTimeString}" Margin="0,0,20,0"/>
                <TextBlock Text="通讯状态: 正常" Margin="0,0,20,0"/>
                <TextBlock Text="TCP服务器: " Margin="0,0,0,0"/>
                <TextBlock Text="{Binding TcpServerStatus}" 
                           Foreground="{Binding IsTcpServerRunning, Converter={StaticResource BoolToColorConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
