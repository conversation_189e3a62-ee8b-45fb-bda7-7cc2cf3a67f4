﻿using ControlTemperature.Communication;
using ControlTemperature.ViewModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ControlTemperature.Helper
{
    public class Gobal
    {
        /// <summary>
        /// 执行文件夹的路径
        /// </summary>
        public static string BinPatn = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");

        /// <summary>
        /// 文件帮助类
        /// </summary>
        public static FileDataHelp FileData = new FileDataHelp();

        /// <summary>
        /// TEC设备数量
        /// </summary>
        private static int tecCount;
        public static int TecCount
        {
            get
            {
                if (tecCount == 0)
                {
                    tecCount = FileData.TecCountLoad().count;
                }
                return tecCount;
            }
            set
            {
                tecCount = value;
            }
        }

        public static ModbusCommon modbusTec = new ModbusCommon();

        public Gobal()
        {
            TecCount = FileData.TecCountLoad().count;
        }
    }
}
