BUILD: [Thrd:Sequence:Type  ] Message
BUILD: [00:0000000000:PROGC ] Build started with parameters:
BUILD: [00:0000000001:PROGC ] Build started in directory: C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600
BUILD: [00:0000000002:PROGC ] Checking for C:\WINCE600\sdk\bin\i386\srccheck.exe.
BUILD: [00:0000000003:PROGC ] Running passes WCEFILES0, MIDL, MC, ASN, THUNK, PRECOMPHEADER, COMPILE, LIB, LINK, MANAGEDRESX, MANAGEDMOD, MANAGEDDLL, MANAGEDEXE, MANAGEDWIN for x86.
BUILD: [00:0000000004:PROGC ] Computing include file dependencies:
BUILD: [00:0000000005:PROGC ] Checking for SDK include directory: C:\WINCE600\sdk\CE\inc.
BUILD: [00:0000000006:PROGC ] Scan C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600\
BUILD: [00:0000000007:PROGC ] Saving C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600\Build.dat.
BUILD: [00:0000000008:INFO  ] Done.
BUILD: [00:0000000009:PROG  ] ' prelink.bat'
BUILD: [01:0000000010:INFO  ] 
BUILD: [01:0000000011:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM (C)2010-13 David Jones 
BUILD: [01:0000000012:INFO  ] 
BUILD: [01:0000000013:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM Thanks to Mike Hall 
BUILD: [01:0000000014:INFO  ] 
BUILD: [01:0000000015:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM CEContentWiz 
BUILD: [01:0000000016:INFO  ] Executing CEContentWiz Prelink.bat
BUILD: [01:0000000017:INFO  ] http://CEContentWiz.codeplex.com
BUILD: [01:0000000018:INFO  ] .
BUILD: [00:0000000019:PROG  ] ' postlink.bat'
BUILD: [01:0000000020:INFO  ] 
BUILD: [01:0000000021:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM (C)2010-13 David Jones 
BUILD: [01:0000000022:INFO  ] 
BUILD: [01:0000000023:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM Thanks to Mike Hall 
BUILD: [01:0000000024:INFO  ] 
BUILD: [01:0000000025:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600>REM CEContentWiz 
BUILD: [01:0000000026:INFO  ] Executing CEContentWiz Postlink.bat
BUILD: [01:0000000027:INFO  ] http://CEContentWiz.codeplex.com
BUILD: [01:0000000028:INFO  ] .
BUILD: [01:0000000029:INFO  ] copying Content Files from Resource Files folder to Targeted Debug Directory
BUILD: [01:0000000030:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600\\Resources\ftdi_ser.dll
BUILD: [01:0000000031:INFO  ]         1 file(s) copied.
BUILD: [01:0000000032:INFO  ] .
BUILD: [01:0000000033:INFO  ] copying Content Files from Resource Files folder to FlatRelease Directory
BUILD: [01:0000000034:INFO  ] C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600\\Resources\ftdi_ser.dll
BUILD: [01:0000000035:INFO  ]         1 file(s) copied.
BUILD: [01:0000000036:INFO  ] .
BUILD: [01:0000000037:INFO  ] Building .cab file
BUILD: [01:0000000038:INFO  ] .
BUILD: [01:0000000039:INFO  ] No file FTDI_VCP_x86_CE600.inf for .cab file generation
BUILD: [01:0000000040:INFO  ] .
BUILD: [01:0000000041:INFO  ] Done Copying
BUILD: [01:0000000042:INFO  ] .
BUILD: [00:0000000043:PROGC ] Saving C:\WINCE600\3rdParty\FTDI_VCP_x86_CE600\Build.dat.
BUILD: [00:0000000044:INFO  ] Done.
BUILD: [00:0000000045:PROGC ] Done.
BUILD: [00:0000000046:PROGC ]                        Files  Warnings  Errors
BUILD: [00:0000000047:PROGC ] Midl                       0         0       0
BUILD: [00:0000000048:PROGC ] Message                    0         0       0
BUILD: [00:0000000049:PROGC ] Precomp Header             0         0       0
BUILD: [00:0000000050:PROGC ] Resource                   0         0       0
BUILD: [00:0000000051:PROGC ] MASM                       0         0       0
BUILD: [00:0000000052:PROGC ] SHASM                      0         0       0
BUILD: [00:0000000053:PROGC ] ARMASM                     0         0       0
BUILD: [00:0000000054:PROGC ] MIPSASM                    0         0       0
BUILD: [00:0000000055:PROGC ] C++                        0         0       0
BUILD: [00:0000000056:PROGC ] C                          0         0       0
BUILD: [00:0000000057:PROGC ] Static Libraries           0         0       0
BUILD: [00:0000000058:PROGC ] Exe's                      0         0       0
BUILD: [00:0000000059:PROGC ] Dll's                      0         0       0
BUILD: [00:0000000060:PROGC ] Preprocess deffile         0         0       0
BUILD: [00:0000000061:PROGC ] Resx                       0         0       0
BUILD: [00:0000000062:PROGC ] CSharp Compile             0         0       0
BUILD: [00:0000000063:PROGC ] Other                      0         0       0
BUILD: [00:0000000064:PROGC ] 
BUILD: [00:0000000065:PROGC ] Total                      0         0       0
BUILD: [00:0000000066:PROGC ] 
BUILD: [00:0000000067:PROGC ] 0 Warnings,  0 Errors
BUILD: [00:0000000068:PROGC ] GetSystemTimes (seconds): Idle: 0     Kernel: 0     User: 0    
BUILD: [00:0000000069:PROGC ] Elapsed  time  (seconds): 0    
