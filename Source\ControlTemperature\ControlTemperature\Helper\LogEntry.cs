using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ControlTemperature.Helper
{
    /// <summary>
    /// 日志条目类，用于高效的日志显示
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// 日志分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 格式化显示文本
        /// </summary>
        public string DisplayText => $"[{Timestamp:HH:mm:ss}] {LogHelp.GetLogLevelText(Level)} {Message}";

        /// <summary>
        /// 日志级别颜色
        /// </summary>
        public string LevelColor => LogHelp.GetLogLevelColor(Level);

        /// <summary>
        /// 构造函数
        /// </summary>
        public LogEntry(string message, LogLevel level, string category = "")
        {
            Timestamp = DateTime.Now;
            Level = level;
            Category = category;
            Message = message;
        }
    }
} 