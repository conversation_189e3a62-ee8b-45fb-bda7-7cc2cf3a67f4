{"Version": 1, "WorkspaceRootPath": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|d:\\c#\\sdvtec\\source\\controltemperature\\controltemperature\\resources\\resources.zh-cn.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\resources\\resources.zh-cn.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\resources\\resources.zh-cn.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\resources\\resources.zh-cn.resx||{81828910-B8B3-4D2B-99A3-067027C180C1}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\communication\\modbuscommon.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\communication\\modbuscommon.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\viewmodel\\channelcontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\viewmodel\\channelcontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\view\\channelcontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\view\\channelcontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\filedatahelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\filedatahelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\viewmodel\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\viewmodel\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\communication\\tcpjsonserver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\communication\\tcpjsonserver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\converters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\converters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\loghelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\loghelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\gobal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\gobal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\notifypropertybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\notifypropertybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\viewmodel\\controllercontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\viewmodel\\controllercontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\helper\\relaycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\helper\\relaycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\view\\controllercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\view\\controllercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\view\\channelcontrol.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\view\\channelcontrol.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\view\\controllercontrol.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\view\\controllercontrol.xaml.cs||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|D:\\C#\\SDVTEC\\Source\\ControlTemperature\\controltemperature\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F1CFC152-CCE0-40C6-96BE-5771D4A9509E}|ControlTemperature\\ControlTemperature.csproj|solutionrelative:controltemperature\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{40ea2e6b-2121-4bb8-a43e-c83c04b51041}"}, {"$type": "Bookmark", "Name": "ST:1352388104:0:{bf84ea66-d821-4dba-b1b1-2777d8574775}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Resources.zh-CN.Designer.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Resources\\Resources.zh-CN.Designer.cs", "RelativeDocumentMoniker": "ControlTemperature\\Resources\\Resources.zh-CN.Designer.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Resources\\Resources.zh-CN.Designer.cs", "RelativeToolTip": "ControlTemperature\\Resources\\Resources.zh-CN.Designer.cs", "ViewState": "AgIAAPAAAAAAAAAAAAAuwPwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T13:25:43.723Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Resources.zh-CN.resx", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Resources\\Resources.zh-CN.resx", "RelativeDocumentMoniker": "ControlTemperature\\Resources\\Resources.zh-CN.resx", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Resources\\Resources.zh-CN.resx", "RelativeToolTip": "ControlTemperature\\Resources\\Resources.zh-CN.resx", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-07-18T13:23:53.705Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "LogHelp.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\LogHelp.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\LogHelp.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\LogHelp.cs", "RelativeToolTip": "ControlTemperature\\Helper\\LogHelp.cs", "ViewState": "AgIAAK8AAAAAAAAAAAAUwLMAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:52:53.215Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "TcpJsonServer.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Communication\\TcpJsonServer.cs", "RelativeDocumentMoniker": "ControlTemperature\\Communication\\TcpJsonServer.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Communication\\TcpJsonServer.cs", "RelativeToolTip": "ControlTemperature\\Communication\\TcpJsonServer.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAjwC0AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T11:38:59.863Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ControllerControl.g.i.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs", "RelativeDocumentMoniker": "ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs", "RelativeToolTip": "ControlTemperature\\obj\\Debug\\View\\ControllerControl.g.i.cs", "ViewState": "AgIAAFUAAAAAAAAAAAAnwGEAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:13:03.269Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "ControlTemperature\\MainWindow.xaml.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\MainWindow.xaml.cs", "RelativeToolTip": "ControlTemperature\\MainWindow.xaml.cs", "ViewState": "AgIAABUAAAAAAAAAAAAAAB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:02:53.775Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "App.xaml", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\App.xaml", "RelativeDocumentMoniker": "ControlTemperature\\App.xaml", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\App.xaml", "RelativeToolTip": "ControlTemperature\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-18T06:59:14.396Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\App.xaml.cs", "RelativeDocumentMoniker": "ControlTemperature\\App.xaml.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\App.xaml.cs", "RelativeToolTip": "ControlTemperature\\App.xaml.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAuwBkAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:58:27.307Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "RelayCommand.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\RelayCommand.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\RelayCommand.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\RelayCommand.cs", "RelativeToolTip": "ControlTemperature\\Helper\\RelayCommand.cs", "ViewState": "AgIAACMAAAAAAAAAAAD4vzAAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:37:11.286Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "NotifyPropertyBase.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\NotifyPropertyBase.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\NotifyPropertyBase.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\NotifyPropertyBase.cs", "RelativeToolTip": "ControlTemperature\\Helper\\NotifyPropertyBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:53:19.882Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "ChannelControl.xaml.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ChannelControl.xaml.cs", "RelativeDocumentMoniker": "ControlTemperature\\View\\ChannelControl.xaml.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ChannelControl.xaml.cs", "RelativeToolTip": "ControlTemperature\\View\\ChannelControl.xaml.cs", "ViewState": "AgIAAAsAAAAAAAAAAIAwwBYAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:42:18.059Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Converters.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\Converters.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\Converters.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\Converters.cs", "RelativeToolTip": "ControlTemperature\\Helper\\Converters.cs", "ViewState": "AgIAACoAAAAAAAAAAAAhwDIAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:31:17.264Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "ControlTemperature\\ViewModel\\MainWindowViewModel.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\MainWindowViewModel.cs", "RelativeToolTip": "ControlTemperature\\ViewModel\\MainWindowViewModel.cs", "ViewState": "AgIAAGoAAAAAAAAAAAAMwGsBAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:22:44.097Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "ControllerControlViewModel.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\ControllerControlViewModel.cs", "RelativeDocumentMoniker": "ControlTemperature\\ViewModel\\ControllerControlViewModel.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\ControllerControlViewModel.cs", "RelativeToolTip": "ControlTemperature\\ViewModel\\ControllerControlViewModel.cs", "ViewState": "AgIAAN0AAAAAAAAAAAAAwOcAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:22:40.867Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ChannelControlViewModel.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\ChannelControlViewModel.cs", "RelativeDocumentMoniker": "ControlTemperature\\ViewModel\\ChannelControlViewModel.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\ViewModel\\ChannelControlViewModel.cs", "RelativeToolTip": "ControlTemperature\\ViewModel\\ChannelControlViewModel.cs", "ViewState": "AgIAAPUCAAAAAAAAAAAUwDgDAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:22:40.262Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ChannelControl.xaml", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ChannelControl.xaml", "RelativeDocumentMoniker": "ControlTemperature\\View\\ChannelControl.xaml", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ChannelControl.xaml", "RelativeToolTip": "ControlTemperature\\View\\ChannelControl.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-18T06:19:21.373Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\MainWindow.xaml", "RelativeDocumentMoniker": "ControlTemperature\\MainWindow.xaml", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\MainWindow.xaml", "RelativeToolTip": "ControlTemperature\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-18T06:18:55.673Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ControllerControl.xaml.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ControllerControl.xaml.cs", "RelativeDocumentMoniker": "ControlTemperature\\View\\ControllerControl.xaml.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ControllerControl.xaml.cs", "RelativeToolTip": "ControlTemperature\\View\\ControllerControl.xaml.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAiwBgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:17:47.039Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ModbusCommon.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Communication\\ModbusCommon.cs", "RelativeDocumentMoniker": "ControlTemperature\\Communication\\ModbusCommon.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Communication\\ModbusCommon.cs", "RelativeToolTip": "ControlTemperature\\Communication\\ModbusCommon.cs", "ViewState": "AgIAAEcAAAAAAAAAAAAQwE8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:17:30.66Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ControllerControl.xaml", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ControllerControl.xaml", "RelativeDocumentMoniker": "ControlTemperature\\View\\ControllerControl.xaml", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\View\\ControllerControl.xaml", "RelativeToolTip": "ControlTemperature\\View\\ControllerControl.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-18T06:17:22.815Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Gobal.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\Gobal.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\Gobal.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\Gobal.cs", "RelativeToolTip": "ControlTemperature\\Helper\\Gobal.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAwAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:17:21.504Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "FileDataHelp.cs", "DocumentMoniker": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\FileDataHelp.cs", "RelativeDocumentMoniker": "ControlTemperature\\Helper\\FileDataHelp.cs", "ToolTip": "D:\\C#\\SDVTEC\\Source\\ControlTemperature\\ControlTemperature\\Helper\\FileDataHelp.cs", "RelativeToolTip": "ControlTemperature\\Helper\\FileDataHelp.cs", "ViewState": "AgIAAI0AAAAAAAAAAAAAAJAAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T06:16:07.707Z"}]}]}]}