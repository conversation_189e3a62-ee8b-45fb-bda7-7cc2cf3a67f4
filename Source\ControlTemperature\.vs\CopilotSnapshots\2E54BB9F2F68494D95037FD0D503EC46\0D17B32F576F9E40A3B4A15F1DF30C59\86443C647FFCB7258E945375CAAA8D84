﻿public partial class ChannelControl : UserControl
{
    public static readonly DependencyProperty ChannelNumberProperty =
        DependencyProperty.Register("ChannelNumber", typeof(int), typeof(ChannelControl), new PropertyMetadata(1));

    public static readonly DependencyProperty ChannelNameProperty =
        DependencyProperty.Register("ChannelName", typeof(string), typeof(ChannelControl), new PropertyMetadata("通道"));

    public static readonly DependencyProperty HeaderColorProperty =
        DependencyProperty.Register("HeaderColor", typeof(string), typeof(ChannelControl), new PropertyMetadata("#000000"));

    public int ChannelNumber
    {
        get { return (int)GetValue(ChannelNumberProperty); }
        set { SetValue(ChannelNumberProperty, value); }
    }

    public string ChannelName
    {
        get { return (string)GetValue(ChannelNameProperty); }
        set { SetValue(ChannelNameProperty, value); }
    }

    public string HeaderColor
    {
        get { return (string)GetValue(HeaderColorProperty); }
        set { SetValue(HeaderColorProperty, value); }
    }

    public ChannelControl()
    {
        InitializeComponent();
        this.DataContext = this;
    }
}

<UserControl x:Class="ControlTemperature.View.ChannelControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ControlTemperature.View"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="400">
    <Border BorderBrush="{Binding HeaderColor}" BorderThickness="1" CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 头部 -->
            <Border Grid.Row="0" Background="{Binding HeaderColor}" Padding="10">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding ChannelName}" Foreground="White" FontWeight="Bold"/>
                    <TextBlock Text=" - " Foreground="White" FontWeight="Bold"/>
                    <TextBlock Text="{Binding ChannelNumber}" Foreground="White" FontWeight="Bold"/>
                </StackPanel>
            </Border>

            <!-- 内容区域 -->
            <Grid Grid.Row="1" Margin="10">
                <!-- 在这里添加通道的具体内容 -->
            </Grid>
        </Grid>
    </Border>
</UserControl>