﻿using ControlTemperature.ViewModel;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace ControlTemperature
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// TEC通道控件的视图模型
        /// </summary>
        public static ChannelControlViewModel channelControlViewModel = new ChannelControlViewModel();

        /// <summary>
        /// 控制器控制视图模型
        /// </summary>
        public static ControllerControlViewModel controllerControlViewModel = new ControllerControlViewModel();
    }
}
