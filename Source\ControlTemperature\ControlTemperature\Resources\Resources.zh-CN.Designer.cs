//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ControlTemperature.Resources {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources_zh_CN {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources_zh_CN() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ControlTemperature.Resources.Resources.zh-CN", typeof(Resources_zh_CN).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 使能 的本地化字符串。
        /// </summary>
        internal static string Channel_Enable {
            get {
                return ResourceManager.GetString("Channel_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 急停 的本地化字符串。
        /// </summary>
        internal static string Channel_EmergencyStop {
            get {
                return ResourceManager.GetString("Channel_EmergencyStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前温度 的本地化字符串。
        /// </summary>
        internal static string Channel_CurrentTemperature {
            get {
                return ResourceManager.GetString("Channel_CurrentTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 目标温度 的本地化字符串。
        /// </summary>
        internal static string Channel_TargetTemperature {
            get {
                return ResourceManager.GetString("Channel_TargetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 温度设置 的本地化字符串。
        /// </summary>
        internal static string Channel_TemperatureSettings {
            get {
                return ResourceManager.GetString("Channel_TemperatureSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置温度: 的本地化字符串。
        /// </summary>
        internal static string Channel_SetTemperature {
            get {
                return ResourceManager.GetString("Channel_SetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 上限温度: 的本地化字符串。
        /// </summary>
        internal static string Channel_MaxTemperature {
            get {
                return ResourceManager.GetString("Channel_MaxTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 下限温度: 的本地化字符串。
        /// </summary>
        internal static string Channel_MinTemperature {
            get {
                return ResourceManager.GetString("Channel_MinTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置 的本地化字符串。
        /// </summary>
        internal static string Channel_SetButton {
            get {
                return ResourceManager.GetString("Channel_SetButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 输出状态 的本地化字符串。
        /// </summary>
        internal static string Channel_OutputStatus {
            get {
                return ResourceManager.GetString("Channel_OutputStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 运行模式: 的本地化字符串。
        /// </summary>
        internal static string Channel_RunningMode {
            get {
                return ResourceManager.GetString("Channel_RunningMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 风扇状态: 的本地化字符串。
        /// </summary>
        internal static string Channel_FanStatus {
            get {
                return ResourceManager.GetString("Channel_FanStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 实时日志 的本地化字符串。
        /// </summary>
        internal static string Channel_RealtimeLog {
            get {
                return ResourceManager.GetString("Channel_RealtimeLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 清空 的本地化字符串。
        /// </summary>
        internal static string Channel_ClearLog {
            get {
                return ResourceManager.GetString("Channel_ClearLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 温控平台控制系统 的本地化字符串。
        /// </summary>
        internal static string MainWindow_Title {
            get {
                return ResourceManager.GetString("MainWindow_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 连接设备 的本地化字符串。
        /// </summary>
        internal static string MainWindow_ConnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_ConnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 断开连接 的本地化字符串。
        /// </summary>
        internal static string MainWindow_DisconnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_DisconnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 连接状态: 的本地化字符串。
        /// </summary>
        internal static string MainWindow_ConnectionStatus {
            get {
                return ResourceManager.GetString("MainWindow_ConnectionStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 活动设备: 的本地化字符串。
        /// </summary>
        internal static string MainWindow_ActiveDevices {
            get {
                return ResourceManager.GetString("MainWindow_ActiveDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP服务器: 的本地化字符串。
        /// </summary>
        internal static string MainWindow_TcpServer {
            get {
                return ResourceManager.GetString("MainWindow_TcpServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 语言: 的本地化字符串。
        /// </summary>
        internal static string MainWindow_Language {
            get {
                return ResourceManager.GetString("MainWindow_Language", resourceCulture);
            }
        }
    }
}
