﻿using HYC.HTLog;
using HYC.HTLog.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ControlTemperature.Helper
{
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }

    /// <summary>
    /// 日志事件参数
    /// </summary>
    public class LogEventArgs : EventArgs
    {
        public string Message { get; set; }
        public LogLevel Level { get; set; }
        public string Category { get; set; }
        public DateTime Timestamp { get; set; }

        public LogEventArgs(string message, LogLevel level, string category)
        {
            Message = message;
            Level = level;
            Category = category;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 增强的日志帮助类 - 基于HYC.HTLog库
    /// </summary>
    public class LogHelp
    {
        /// <summary>
        /// 日志事件，用于界面显示
        /// </summary>
        public static event EventHandler<LogEventArgs> LogEvent;

        /// <summary>
        /// 日志分类常量
        /// </summary>
        public static class Categories
        {
            public const string System = "System";
            public const string TecCommunication = "TecCommunication";
            public const string TcpCommunication = "TcpCommunication";
            public const string DeviceOperation = "DeviceOperation";
            public const string Error = "Error";
            public const string Warning = "Warning";
            public const string Debug = "Debug";
        }

        /// <summary>
        /// 静态构造函数，初始化日志系统
        /// </summary>
        static LogHelp()
        {
            InitializeLogSystem();
        }

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        private static void InitializeLogSystem()
        {
            try
            {
                // 设置日志输出等级为DEBUG，记录所有级别的日志
                LogManager.SetDegree(MessageDegree.DEBUG);
                
                // 设置日志输出路径
                LogManager.SetLogPath("Log");

                // 初始化各类日志记录器，使用分类存储
                LogManager.InitLogger(Categories.System, "系统", false);
                LogManager.InitLogger(Categories.TecCommunication, "TEC通信", false);
                LogManager.InitLogger(Categories.TcpCommunication, "TCP通信", false);
                LogManager.InitLogger(Categories.DeviceOperation, "设备操作", false);
                LogManager.InitLogger(Categories.Error, "错误", false);
                LogManager.InitLogger(Categories.Warning, "警告", false);
                LogManager.InitLogger(Categories.Debug, "调试", false);

                // 定期清理旧日志（保留30天）
                LogManager.DeleteOldLog(30);
            }
            catch (Exception ex)
            {
                // 初始化失败时使用默认日志
                Console.WriteLine($"日志系统初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 触发日志事件
        /// </summary>
        private static void OnLogEvent(string message, LogLevel level, string category)
        {
            LogEvent?.Invoke(null, new LogEventArgs(message, level, category));
        }

        /// <summary>
        /// 写入日志的通用方法
        /// </summary>
        private static void WriteLog(string message, LogLevel level, string category)
        {
            try
            {
                var logger = LogManager.GetLogger(category);
                switch (level)
                {
                    case LogLevel.Debug:
                        logger.Debug(message);
                        break;
                    case LogLevel.Info:
                        logger.Info(message);
                        break;
                    case LogLevel.Warning:
                        logger.Warn(message);
                        break;
                    case LogLevel.Error:
                        logger.Error(message);
                        break;
                }
                OnLogEvent(message, level, GetCategoryDisplayName(category));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取分类显示名称
        /// </summary>
        private static string GetCategoryDisplayName(string category)
        {
            switch (category)
            {
                case Categories.System:
                    return "系统";
                case Categories.TecCommunication:
                    return "TEC通信";
                case Categories.TcpCommunication:
                    return "TCP通信";
                case Categories.DeviceOperation:
                    return "设备操作";
                case Categories.Error:
                    return "错误";
                case Categories.Warning:
                    return "警告";
                case Categories.Debug:
                    return "调试";
                default:
                    return category;
            }
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        public static void Debug(string message, string category = Categories.Debug)
        {
            WriteLog(message, LogLevel.Debug, category);
        }

        /// <summary>
        /// 信息日志
        /// </summary>
        public static void Info(string message, string category = Categories.System)
        {
            WriteLog(message, LogLevel.Info, category);
        }

        /// <summary>
        /// 警告日志
        /// </summary>
        public static void Warning(string message, string category = Categories.Warning)
        {
            WriteLog(message, LogLevel.Warning, category);
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        public static void Error(string message, string category = Categories.Error)
        {
            WriteLog(message, LogLevel.Error, category);
        }

        /// <summary>
        /// 错误日志（包含异常）
        /// </summary>
        public static void Error(string message, Exception ex, string category = Categories.Error)
        {
            var fullMessage = $"{message}\n异常详情：{ex.Message}\n堆栈跟踪：{ex.StackTrace}";
            WriteLog(fullMessage, LogLevel.Error, category);
        }

        /// <summary>
        /// TEC通信日志
        /// </summary>
        public static void TecCommunication(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog(message, level, Categories.TecCommunication);
        }

        /// <summary>
        /// TCP通信日志
        /// </summary>
        public static void TcpCommunication(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog(message, level, Categories.TcpCommunication);
        }

        /// <summary>
        /// 系统日志
        /// </summary>
        public static void System(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog(message, level, Categories.System);
        }

        /// <summary>
        /// 设备操作日志
        /// </summary>
        public static void DeviceOperation(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog(message, level, Categories.DeviceOperation);
        }

        /// <summary>
        /// 获取日志级别的显示颜色
        /// </summary>
        public static string GetLogLevelColor(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Debug:
                    return "#6C757D"; // 灰色
                case LogLevel.Info:
                    return "#007BFF"; // 蓝色
                case LogLevel.Warning:
                    return "#FFC107"; // 黄色
                case LogLevel.Error:
                    return "#DC3545"; // 红色
                default:
                    return "#000000"; // 黑色
            }
        }

        /// <summary>
        /// 获取日志级别的显示文本
        /// </summary>
        public static string GetLogLevelText(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Debug:
                    return "[调试]";
                case LogLevel.Info:
                    return "[信息]";
                case LogLevel.Warning:
                    return "[警告]";
                case LogLevel.Error:
                    return "[错误]";
                default:
                    return "[未知]";
            }
        }

        /// <summary>
        /// 设置日志输出等级
        /// </summary>
        public static void SetLogLevel(LogLevel level)
        {
            MessageDegree degree = MessageDegree.DEBUG;
            switch (level)
            {
                case LogLevel.Debug:
                    degree = MessageDegree.DEBUG;
                    break;
                case LogLevel.Info:
                    degree = MessageDegree.INFO;
                    break;
                case LogLevel.Warning:
                    degree = MessageDegree.WARN;
                    break;
                case LogLevel.Error:
                    degree = MessageDegree.ERROR;
                    break;
            }
            LogManager.SetDegree(degree);
        }

        /// <summary>
        /// 清理旧日志
        /// </summary>
        public static void CleanOldLogs(uint days = 30)
        {
            LogManager.DeleteOldLog(days);
        }
    }
}
