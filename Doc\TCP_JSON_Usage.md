# TCP JSON通信使用说明

## 概述
TEC控制软件现在支持TCP JSON通信，允许线体软件通过TCP连接发送JSON命令来控制和监控TEC设备。

## 系统架构
```
线体软件(客户端) → TCP(8888端口) → TEC控制软件(服务器) → Modbus RTU → TEC控制器
```

## 功能特性

### ✅ 已实现的功能
1. **TCP服务器**：监听8888端口，支持多客户端连接
2. **JSON协议**：统一的JSON命令格式，支持多控制器、多通道
3. **环境温度推送**：线体软件推送环境温度到TEC控制器
4. **设备控制**：目标温度设置、使能控制、温度上下限设置
5. **状态查询**：实时温度读取、状态查询
6. **错误处理**：完整的错误处理和日志记录

### ✅ 已修复的编译错误
1. 添加了缺失的 `SetChannelMaxTemperature` 方法
2. 添加了缺失的 `SetChannelMinTemperature` 方法
3. 所有转换器类正确引用（`BoolToConnectionStatusConverter`、`BoolToColorConverter`等）
4. 所有视图类正确引用（`ControllerControl`、`ChannelControl`）

## 使用方法

### 1. 启动TEC控制软件
- 启动后TCP服务器自动运行在8888端口
- 在界面上可以看到"TCP服务器：已启动（端口：8888）"

### 2. 线体软件连接
```csharp
// C# 示例代码
var client = new TcpClient();
await client.ConnectAsync("127.0.0.1", 8888);
var stream = client.GetStream();
```

### 3. 发送JSON命令

#### 设置环境温度
```json
{
    "type": "ambientTemp",
    "unit": 1,
    "channel": 1,
    "value": 22.3,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 设置目标温度
```json
{
    "type": "setTargetTemp",
    "unit": 1,
    "channel": 1,
    "value": 25.0
}
```

#### 开关使能控制
```json
{
    "type": "enable",
    "unit": 1,
    "channel": 1,
    "value": true
}
```

#### 设置温度上限
```json
{
    "type": "setMaxTemp",
    "unit": 1,
    "channel": 1,
    "value": 85.0
}
```

#### 设置温度下限
```json
{
    "type": "setMinTemp",
    "unit": 1,
    "channel": 1,
    "value": -20.0
}
```

#### 读取当前温度
```json
{
    "type": "getCurrentTemp",
    "unit": 1,
    "channel": 1
}
```

#### 读取所有状态
```json
{
    "type": "getAllStatus",
    "unit": 1
}
```

### 4. 接收响应

#### 成功响应
```json
{
    "result": "success",
    "message": "操作成功",
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 错误响应
```json
{
    "result": "error",
    "message": "错误信息",
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 温度数据响应
```json
{
    "result": "success",
    "data": {
        "currentTemp": 25.123,
        "targetTemp": 25.000,
        "enabled": true
    },
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

## 测试工具

### 使用内置测试客户端
项目包含了一个测试客户端类 `TestClient`，可以用来测试TCP通信功能：

```csharp
// 创建测试客户端
var testClient = new TestClient("127.0.0.1", 8888);

// 运行所有测试
await testClient.RunAllTests();
```

### 测试步骤
1. 启动TEC控制软件
2. 确认TCP服务器已启动
3. 运行测试客户端
4. 观察通信日志和响应

## 地址映射

### 重要的Modbus地址
- **40032 (地址31)**：通道1外部温度输入
- **40034 (地址33)**：通道2外部温度输入
- **40001 (地址0)**：温度上限设置
- **40003 (地址2)**：温度下限设置
- **40005 (地址4)**：通道1目标温度
- **40019 (地址18)**：通道2目标温度

### 控制器和通道编号
- **控制器编号**：1-8（最多8个控制器）
- **通道编号**：1-2（每个控制器2个通道）

## 错误处理

### 常见错误
1. **控制器编号超出范围**：确保unit在1-8之间
2. **通道编号超出范围**：确保channel在1-2之间
3. **控制器未连接**：检查Modbus RTU连接
4. **缺少参数**：确保JSON包含所有必需字段

### 日志记录
所有通信活动都会记录到：
- 界面日志显示
- 日志文件：`Log/YYYYMMDD/TecCommunication_YYYYMMDD.log`

## 性能和限制

### 性能参数
- **连接端口**：8888
- **编码格式**：UTF-8
- **超时时间**：5秒
- **最大客户端**：理论上无限制（实际受系统资源限制）

### 使用限制
1. 每次只能设置一个通道的参数
2. 温度上下限是设备级别的设置，影响所有通道
3. 环境温度输入是通道级别的设置

## 故障排除

### 常见问题及解决方案

**1. 无法连接到TCP服务器**
- 检查防火墙是否允许8888端口
- 确认TEC控制软件已启动
- 检查IP地址和端口是否正确

**2. 发送命令无响应**
- 检查JSON格式是否正确
- 确认控制器已连接
- 查看日志文件了解详细错误

**3. 温度设置不生效**
- 确认设备已连接
- 检查Modbus RTU通信状态
- 验证参数范围是否合理

**4. 编译错误**
- 确认所有必需的文件都已包含在项目中
- 检查命名空间引用是否正确
- 重新生成项目

## 扩展功能

### 未来可能的扩展
1. **认证机制**：添加客户端认证
2. **心跳检测**：定期检查连接状态
3. **批量操作**：支持一次设置多个通道
4. **状态推送**：主动推送设备状态变化
5. **配置管理**：动态修改TCP服务器设置

## 联系支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 设备连接状态
3. JSON命令格式是否正确
4. 网络连接是否正常 