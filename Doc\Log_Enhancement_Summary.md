# 日志功能改进总结

## 概述
基于HYC.HTLog库重新设计了日志系统，实现了分类存储、界面显示和完整的日志记录功能。

## 主要改进

### 1. 日志分类系统
#### 分类定义
- **系统日志 (System)**: 系统启动、关闭、配置加载等
- **TEC通信 (TecCommunication)**: TEC设备的Modbus RTU通信
- **TCP通信 (TcpCommunication)**: TCP JSON服务器通信
- **设备操作 (DeviceOperation)**: 设备控制操作记录
- **错误日志 (Error)**: 所有错误信息集中记录
- **警告日志 (Warning)**: 警告信息记录
- **调试日志 (Debug)**: 调试信息记录

#### 分类存储
```csharp
// 初始化各类日志记录器，使用分类存储
LogManager.InitLogger(Categories.System, "系统", false);
LogManager.InitLogger(Categories.TecCommunication, "TEC通信", false);
LogManager.InitLogger(Categories.TcpCommunication, "TCP通信", false);
LogManager.InitLogger(Categories.DeviceOperation, "设备操作", false);
LogManager.InitLogger(Categories.Error, "错误", false);
LogManager.InitLogger(Categories.Warning, "警告", false);
LogManager.InitLogger(Categories.Debug, "调试", false);
```

### 2. 日志级别管理
#### 级别定义
- **Debug**: 调试信息，开发阶段使用
- **Info**: 一般信息，正常操作记录
- **Warning**: 警告信息，需要注意的情况
- **Error**: 错误信息，需要处理的问题

#### 级别映射
```csharp
LogLevel.Debug → MessageDegree.DEBUG
LogLevel.Info → MessageDegree.INFO
LogLevel.Warning → MessageDegree.WARN
LogLevel.Error → MessageDegree.ERROR
```

### 3. 界面显示改进
#### 字体调整
- **日志标题**: 字体大小从11→12
- **日志内容**: 字体大小从8.5→11
- **行高**: 从10→14，提升可读性
- **按钮尺寸**: 清空按钮从40×18→45×20

#### 显示格式
```
[时间] [级别] [分类] 消息内容
```

### 4. 完整的日志记录

#### 系统级日志
- 系统启动和关闭
- 配置文件加载
- 控制器初始化
- 定时器管理
- TCP服务器状态

#### TEC通信日志
- 设备连接状态
- 数据读写操作
- 通信错误记录
- 参数设置记录

#### TCP通信日志
- 服务器启动停止
- 客户端连接断开
- 消息收发记录
- 错误处理记录

#### 设备操作日志
- 温度设置操作
- 使能控制操作
- 参数调整记录
- 操作结果记录

### 5. 代码实现特点

#### 统一的日志接口
```csharp
// 通用日志方法
LogHelp.System("消息", LogLevel.Info);
LogHelp.TecCommunication("消息", LogLevel.Info);
LogHelp.TcpCommunication("消息", LogLevel.Info);
LogHelp.DeviceOperation("消息", LogLevel.Info);
LogHelp.Error("错误消息");
LogHelp.Warning("警告消息");
LogHelp.Debug("调试消息");
```

#### 事件驱动界面更新
```csharp
// 日志事件，用于界面显示
public static event EventHandler<LogEventArgs> LogEvent;

// 在ViewModel中订阅事件
LogHelp.LogEvent += OnLogEvent;
```

#### 自动日志管理
```csharp
// 自动清理旧日志（保留30天）
LogManager.DeleteOldLog(30);

// 限制界面日志行数，防止内存过大
if (lines.Length > MAX_LOG_LINES)
{
    var keepLines = lines.Skip(lines.Length - MAX_LOG_LINES).ToArray();
    LogMessages = string.Join("\n", keepLines);
}
```

### 6. 日志文件结构
```
Log/
├── 系统/
│   └── System_YYYYMMDD.log
├── TEC通信/
│   └── TecCommunication_YYYYMMDD.log
├── TCP通信/
│   └── TcpCommunication_YYYYMMDD.log
├── 设备操作/
│   └── DeviceOperation_YYYYMMDD.log
├── 错误/
│   └── Error_YYYYMMDD.log
├── 警告/
│   └── Warning_YYYYMMDD.log
└── 调试/
    └── Debug_YYYYMMDD.log
```

## 使用示例

### 1. 系统操作记录
```csharp
LogHelp.System("TEC控制系统启动", LogLevel.Info);
LogHelp.System("配置文件加载完成", LogLevel.Info);
LogHelp.System("初始化了 8 个控制器", LogLevel.Info);
```

### 2. 设备通信记录
```csharp
LogHelp.TecCommunication("TEC驱动器 1 连接成功，串口：COM1，从站地址：1");
LogHelp.TecCommunication("通道 1 目标温度设置为：25.0℃");
LogHelp.TecCommunication("通道 1 使能状态设置为：开启");
```

### 3. TCP通信记录
```csharp
LogHelp.TcpCommunication("TCP服务器已启动，端口：8888");
LogHelp.TcpCommunication("客户端已连接：192.168.1.100:12345");
LogHelp.TcpCommunication("收到JSON命令：环境温度设置", LogLevel.Debug);
```

### 4. 错误记录
```csharp
LogHelp.Error("TEC驱动器 1 连接失败：端口占用");
LogHelp.Error("TCP服务器启动失败：端口8888已被占用");
LogHelp.Error("设置目标温度失败：设备未响应", ex);
```

## 优势特点

### 1. 分类清晰
- 不同类型的日志存储在不同文件中
- 便于问题定位和分析
- 支持按类型查看日志

### 2. 界面友好
- 字体大小适中，易于阅读
- 实时显示重要日志信息
- 支持日志清空功能

### 3. 性能优化
- 异步日志写入，不阻塞主线程
- 自动日志轮转和清理
- 内存使用控制

### 4. 扩展性强
- 易于添加新的日志分类
- 支持动态调整日志级别
- 可配置的日志保留策略

### 5. 错误追踪
- 完整的异常信息记录
- 堆栈跟踪信息保存
- 多级别错误分类

## 注意事项

1. **日志级别**: 生产环境建议使用INFO级别，开发环境可使用DEBUG级别
2. **存储空间**: 定期清理旧日志文件，避免磁盘空间不足
3. **性能影响**: 过多的DEBUG级别日志可能影响性能
4. **敏感信息**: 避免记录敏感数据如密码等信息

## 维护建议

1. **定期检查**: 定期检查日志文件的大小和数量
2. **错误分析**: 定期分析错误日志，及时处理问题
3. **性能监控**: 监控日志系统的性能影响
4. **级别调整**: 根据需要调整日志级别和保留策略 