﻿public partial class ChannelControl : UserControl
{
    public static readonly DependencyProperty ChannelNumberProperty =
        DependencyProperty.Register("ChannelNumber", typeof(int), typeof(ChannelControl), new PropertyMetadata(1));

    public static readonly DependencyProperty ChannelNameProperty =
        DependencyProperty.Register("ChannelName", typeof(string), typeof(ChannelControl), new PropertyMetadata("通道"));

    public static readonly DependencyProperty HeaderColorProperty =
        DependencyProperty.Register("HeaderColor", typeof(string), typeof(ChannelControl), new PropertyMetadata("#000000"));

    public int ChannelNumber
    {
        get { return (int)GetValue(ChannelNumberProperty); }
        set { SetValue(ChannelNumberProperty, value); }
    }

    public string ChannelName
    {
        get { return (string)GetValue(ChannelNameProperty); }
        set { SetValue(ChannelNameProperty, value); }
    }

    public string HeaderColor
    {
        get { return (string)GetValue(HeaderColorProperty); }
        set { SetValue(HeaderColorProperty, value); }
    }

    public ChannelControl()
    {
        InitializeComponent();
        this.DataContext = this;
    }
}