﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Permissions;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace ControlTemperature.Helper
{
    public class FileDataHelp
    {
        /// <summary>
        /// 保存TEC设备的Modbus数据到JSON文件
        /// </summary>
        public void TecRtuDataSave()
        {
            try
            {
                string path = Path.Combine(Gobal.BinPatn, $"TecModbusData.json");
                TecModbusData[] arrayData = new TecModbusData[Gobal.TecCount];
                for (int c = 0; c < Gobal.TecCount; c++)
                {
                    arrayData[c] = new TecModbusData
                    {
                        Port = $"COM{c}", // 示例端口号
                        SlaveId = 1, // 示例从站地址
                        BaudRate = 115200 // 示例波特率
                    };
                }
                SaveToFile(arrayData, path);
            }
            catch (Exception ex)
            {
                LogHelp.Warning($"保存TecModbusData.json失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载TEC设备的Modbus数据
        /// </summary>
        /// <returns></returns>
        public TecModbusData[] TecRTUDataLoad()
        {
            TecModbusData[] arrayData = new TecModbusData[Gobal.TecCount];
            try
            {
                string path = Path.Combine(Gobal.BinPatn, $"TecModbusData.json");
                if (File.Exists(path) == false)
                {
                    TecRtuDataSave(); // 如果文件不存在，先保存默认数据
                }
                arrayData = LoadFromFile<TecModbusData[]>(path);
                return arrayData;
            }
            catch (Exception ex)
            {
                LogHelp.Warning($"加载TecModbusData.json失败: {ex.Message}");
                return null;
            }
        }

        public TecCount TecCountLoad()
        {
            try
            {
                string path = Path.Combine(Gobal.BinPatn, "TecCount.json");
                if (File.Exists(path) == false)
                {
                    TecCount data = new TecCount
                    {
                        count = 8 // 默认值
                    };
                    SaveToFile(data, path);
                }
                return LoadFromFile<TecCount>(path);
            }
            catch (Exception ex)
            {
                LogHelp.Warning($"加载TecCount.json失败: {ex.Message}");
                return default(TecCount);
            }
        }

        /// <summary>
        /// 默认的JSON序列化设置
        /// </summary>
        private static readonly JsonSerializerSettings DefaultSettings = new JsonSerializerSettings
        {
            // 格式化输出
            Formatting = Formatting.Indented,
            // 忽略null值
            NullValueHandling = NullValueHandling.Ignore,
            // 日期格式
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            // 忽略循环引用
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            // 错误处理
            Error = (sender, args) =>
            {
                // 跳过错误属性，继续序列化
                args.ErrorContext.Handled = true;
            }
        };

        /// <summary>
        /// 将对象序列化为JSON并保存到文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="settings">序列化设置（可选）</param>
        /// <returns>是否成功</returns>
        public static bool SaveToFile<T>(T obj, string filePath, JsonSerializerSettings settings = null)
        {
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 序列化对象
                string jsonString = JsonConvert.SerializeObject(obj, settings ?? DefaultSettings);

                // 写入文件
                File.WriteAllText(filePath, jsonString, System.Text.Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"保存文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从文件中读取JSON并反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <param name="settings">反序列化设置（可选）</param>
        /// <returns>反序列化后的对象，失败时返回default(T)</returns>
        public static T LoadFromFile<T>(string filePath, JsonSerializerSettings settings = null)
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"文件不存在: {filePath}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return default(T);
                }

                // 读取文件内容
                string jsonString = File.ReadAllText(filePath, System.Text.Encoding.UTF8);

                // 反序列化
                return JsonConvert.DeserializeObject<T>(jsonString, settings ?? DefaultSettings);
            }
            catch (Exception ex)
            {
                throw new Exception($"加载文件失败: {ex.Message}", ex);
            }
        }



    }

    public class TecModbusData
    {
        /// <summary>
        /// 端口号 
        /// </summary>
        public string Port { get; set; }

        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveId { get; set; } = 1;

        /// <summary>
        /// 从站地址
        /// </summary>
        public int BaudRate { get; set; } = 115200;
    }

    public class TecCount
    {
        public int count { get; set; } = 8;
    }
}
