<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HYC.HTCommunication</name>
    </assembly>
    <members>
        <member name="T:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort">
            <summary>
            串口通信类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.IsOpen">
            <summary>
            端口打开状态
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.SleepTime">
            <summary>
            连续串口缓冲数据检测的间隔时间，默认30ms
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.IsEndCharMode">
            <summary>
            使用结尾字符判断接收是否完成，此模式默认关闭
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.EndChar">
            <summary>
            结尾字符，默认为 \r
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Init(HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo)">
            <summary>
            初始化串口
            </summary>
            <param name="info">串口连接信息</param>
            <returns>串口是否已打开</returns>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Open">
            <summary>
            使用已经传入的参数重新初始化串口
            </summary>
            <returns>串口是否已打开</returns>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Send(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Send(System.String)">
            <summary>
            发送文本信息
            </summary>
            <param name="text">文本</param>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.sp_DataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
            <summary>
            接收到数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Close">
            <summary>
            关闭串口
            </summary>
            <returns>串口是否已关闭</returns>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.Dispose">
            <summary>
            释放串口连接和资源
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SerialPortWrapper.HTCOMMSerialPort.DataReceived">
            <summary>
            接收到数据
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo">
            <summary>
            串口连接参数类，默认8/1/N
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.PortName">
            <summary>
            端口号，例 “COM1”
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.BaudRate">
            <summary>
            波特率，9600,19200,115200等
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.DataBits">
            <summary>
            数据位，8/7/6
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.StopBits">
            <summary>
            停止位，StopBits.One/ StopBits.Two/ StopBits.OnePointFive
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.Parity">
            <summary>
            校验位
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.DtrEnable">
            <summary>
            Data Terminal Ready
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.RtsEnable">
            <summary>
            Request to Transmit
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.Handshake">
            <summary>
            握手协议
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.SleepTime">
            <summary>
            连续串口缓冲数据检测的间隔时间，默认30ms
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.#ctor(System.String,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            构造函数，可指定参数
            </summary>
            <param name="portName">端口号，例 “COM1”</param>
            <param name="baudRate">波特率，9600,19200,115200等</param>
            <param name="dataBits">数据位，8/7/6</param>
            <param name="stopBits">停止位，1/1.5/2或者One/OnePointFive/Two，不区分大小写</param>
            <param name="parity">校验位，N/E/O 或者 None/Even/Odd，不区分大小写</param>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.GetStopBits(System.String)">
            <summary>
            从字符串获取停止位，输入字符串为1/1.5/2或者One/OnePointFive/Two，不区分大小写
            </summary>
            <param name="value">字符</param>
            <returns> StopBits枚举值</returns>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.HTSerialPortInfo.GetParity(System.String)">
            <summary>
            从字符串获取校验位，输入字符串为 N/E/O 或者 None/Even/Odd，不区分大小写
            </summary>
            <param name="value">字符</param>
            <returns> Parity枚举值</returns>
        </member>
        <member name="T:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs">
            <summary>
            SerialPort事件参数类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs.PortName">
            <summary>
            引发事件的端口号，包含"COM"字符
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs.Datas">
            <summary>
            接收到的数据
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs.#ctor(System.String,System.Byte[])">
            <summary>
            构造函数
            </summary>
            <param name="portName">端口号</param>
            <param name="datas">数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SerialPortWrapper.SerialPortEventArgs.#ctor(System.String,System.Byte[],System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="portName">端口号</param>
            <param name="datas">数据</param>
            <param name="userData">用户数据</param>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket">
            <summary>
            socket数据基类
            </summary>
        </member>
        <member name="F:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.WinNetErrorCode">
            <summary>
            windwos平台 socket断开错误码
            </summary>
        </member>
        <member name="F:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.LinuxNetErrorCode">
            <summary>
            linux平台 socket断开错误码
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.IsStarted">
            <summary>
            socket是否已经启动
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Address">
            <summary>
            Socket地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Port">
            <summary>
            端口，初始化端口
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.ClientConnected">
            <summary>
            Soekct链接成功
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.ClientDisconnected">
            <summary>
            Socket断开连接
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.PrepareSend">
            <summary>
            数据发送前数据
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.CompletedSend">
            <summary>
            数据发送完毕事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.NetError">
            <summary>
            网络错误事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OtherException">
            <summary>
            异常事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.DataRevice">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.#ctor(System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="listenPort">监听的port</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.#ctor(System.Net.IPEndPoint)">
            <summary>
            异步Socket 
            </summary>
            <param name="localEP">监听的终结点</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.#ctor(System.Net.IPAddress,System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="IPAddress">IP</param>
            <param name="listenPort">端口</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.#ctor(System.Net.IPAddress,System.Int32,System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="localIPAddress">监听的IP地址</param>
            <param name="listenPort">监听的port</param>
            <param name="maxClient">最大客户端数量</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Start">
            <summary>
            启动服务器
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Stop">
            <summary>
            停止服务器
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnDataRevice(HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs)">
            <summary>
            数据接收数据
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnOtherException(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            其他异常数据事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnNetError(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            网络错误异常
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnCompletedSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            数据发送完成事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnPrepareSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            数据发送前触发事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnClientConnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            触发链接成功事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.OnClientDisconnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs)">
            <summary>
            触发链接断开事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Send(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="data">数据报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Send(System.Net.Sockets.Socket,System.Byte[])">
            <summary>
            异步发送数据至指定的客户端
            </summary>
            <param name="client">客户端</param>
            <param name="data">报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Close(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            关闭一个与客户端之间的会话
            </summary>
            <param name="state">须要关闭的客户端会话对象</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.CloseAllClient">
            <summary>
            关闭全部的客户端会话,与全部的客户端连接会断开
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocket.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing,
            releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient">
            <summary>
            异步Socket连接
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.IsConnected">
            <summary>
            是否已经链接
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.IsStarted">
            <summary>
            是否已经启动
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Address">
            <summary>
            链接IP
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Port">
            <summary>
            链接端口
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.ClientConnected">
            <summary>
            客户端连接事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.ClientDisconnected">
            <summary>
            客户端断开事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.PrepareSend">
            <summary>
            数据发送前事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.CompletedSend">
            <summary>
            数据发送前事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.NetError">
            <summary>
            网络错误事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.OtherException">
            <summary>
            异常事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.DataRevice">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.#ctor(System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="listenPort">监听的port</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.#ctor(System.Net.IPEndPoint)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="localEP">监听的终结点</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.#ctor(System.Net.IPAddress,System.Int32,System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="localIPAddress">监听的IP地址</param>
            <param name="listenPort">监听的port</param>
            <param name="maxClient">最大客户端数量</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Start">
            <summary>
            启动连接
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Stop">
            <summary>
            停止服务器
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.HandleDataReceived(System.IAsyncResult)">
            <summary>
            处理客户端数据
            </summary>
            <param name="ar"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Send(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="data">数据报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Send(System.Net.Sockets.Socket,System.Byte[])">
            <summary>
            异步发送数据至指定的客户端
            </summary>
            <param name="client">客户端</param>
            <param name="data">报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.SendDataEnd(System.IAsyncResult)">
            <summary>
            发送数据完成处理函数
            </summary>
            <param name="ar">目标客户端Socket</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaiseClientConnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发客户端连接事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaiseClientDisconnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发客户端连接断开事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaisePrepareSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发发送数据前的事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaiseCompletedSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发数据发送完成的事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaiseNetError(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发网络错误事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.RaiseOtherException(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState,System.String)">
            <summary>
            触发异常事件
            </summary>
            <param name="state"></param>
            <param name="descrip">提示信息</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Close(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            关闭一个与客户端之间的会话
            </summary>
            <param name="state">须要关闭的客户端会话对象</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.CloseAllClient">
            <summary>
            关闭全部的客户端会话,与全部的客户端连接会断开
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing,
            releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketClient.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release
            both managed and unmanaged resources; <c>false</c>
            to release only unmanaged resources.</param>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs">
            <summary>
            Socket事件
            </summary>
        </member>
        <member name="F:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.Msg">
            <summary>
            提示信息
            </summary>
        </member>
        <member name="F:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.State">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.IsHandled">
            <summary>
            是否已经处理过了
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="msg">提示信息</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.#ctor(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            构造函数
            </summary>
            <param name="state">数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketEventArgs.#ctor(System.String,HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            构造函数
            </summary>
            <param name="msg">提示信息</param>
            <param name="state">数据</param>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer">
            <summary>
            异步Socket 服务器对象
            </summary>
        </member>
        <member name="F:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.m_ClientSocketSessionDict">
            <summary>
            客户端会话列表
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.IsStarted">
            <summary>
            服务是否已经启动
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Address">
            <summary>
            绑定IP
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Port">
            <summary>
            监听端口
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.ClientConnected">
            <summary>
            Socket链接成功事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.ClientDisconnected">
            <summary>
            Socket链接断开事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.PrepareSend">
            <summary>
            数据发送前事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.CompletedSend">
            <summary>
            数据发送完成事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.NetError">
            <summary>
            网络错误事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.OtherException">
            <summary>
            异常事件
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.DataRevice">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.#ctor(System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="listenPort">监听的port</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.#ctor(System.Net.IPEndPoint)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="localEP">监听的终结点</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.#ctor(System.Net.IPAddress,System.Int32,System.Int32)">
            <summary>
            异步Socket TCP服务器
            </summary>
            <param name="localIPAddress">监听的IP地址</param>
            <param name="listenPort">监听的port</param>
            <param name="maxClient">最大客户端数量</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Start">
            <summary>
            启动服务器
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Stop">
            <summary>
            停止服务器
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.HandleAcceptConnected(System.IAsyncResult)">
            <summary>
            处理客户端连接
            </summary>
            <param name="ar"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.HandleDataReceived(System.IAsyncResult)">
            <summary>
            处理客户端数据
            </summary>
            <param name="ar"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Send(System.Byte[])">
            <summary>
            发送数据，发送给全部连接客户端
            </summary>
            <param name="data">数据报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Send(System.Net.Sockets.Socket,System.Byte[])">
            <summary>
            异步发送数据至指定的客户端
            </summary>
            <param name="client">客户端</param>
            <param name="data">报文</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.SendDataEnd(System.IAsyncResult)">
            <summary>
            发送数据完成处理函数
            </summary>
            <param name="ar">目标客户端Socket</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Decode(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState,System.Int32)">
            <summary>
            收到字节拆包解码
            </summary>
            <param name="state"></param>
            <param name="totalBytes"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaiseClientConnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发客户端连接事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaiseClientDisconnected(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发客户端连接断开事件
            </summary>
            <param name="state">链接断开</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaisePrepareSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发发送数据前的事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaiseCompletedSend(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发数据发送完成的事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaiseNetError(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            触发网络错误事件
            </summary>
            <param name="state"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.RaiseOtherException(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState,System.String)">
            <summary>
            触发异常事件
            </summary>
            <param name="state"></param>
            <param name="descrip">提示信息</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Close(HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState)">
            <summary>
            关闭一个与客户端之间的会话
            </summary>
            <param name="state">须要关闭的客户端会话对象</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.CloseAllClient">
            <summary>
            关闭全部的客户端会话,与全部的客户端连接会断开
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing,
            releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketServer.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release
            both managed and unmanaged resources; <c>false</c>
            to release only unmanaged resources.</param>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState">
            <summary>
            异步SOCKET TCP 中用来存储客户端状态信息的类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.RecvDataBuffer">
            <summary>
            接收数据
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.ClientSocket">
            <summary>
            链接socket对象
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.SessionId">
            <summary>
            链接socket对象ID
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.ID">
            <summary>
            消息事件ID
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.Datagram">
            <summary>
            存取会话的报文
            注意:在有些情况下报文可能仅仅是报文的片断而不完整
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.#ctor(System.Net.Sockets.Socket,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="socket">会话使用的Socket连接</param>
            <param name="session">消息ID</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.InitBuffer">
            <summary>
            初始化数据缓冲区
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncSocketState.Close">
            <summary>
            关闭会话
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.UDPDataRevice">
            <summary>
            数据接收
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="listenPort">端口</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.#ctor(System.Net.IPEndPoint)">
            <summary>
            构造函数
            </summary>
            <param name="localEP">绑定IP和端口 IP，端口</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.#ctor(System.Net.IPAddress,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="IPAddress">绑定IP</param>
            <param name="listenPort">绑定端口</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.#ctor(System.Net.IPAddress,System.Int32,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="localIPAddress">绑定IP</param>
            <param name="listenPort">绑定端口</param>
            <param name="maxClient">没用</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.Start">
            <summary>
            启动监听
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.Async.AsyncUdp.SendAsync(System.Byte[],System.Net.IPEndPoint,System.Action{System.Boolean,System.String})">
            <summary>
            一部发送UDP数据
            </summary>
            <param name="buffer">发送字节</param>
            <param name="remoteIPEndPort">远程IP和端口</param>
            <param name="sendCallBack">发送回掉，成功和失败</param>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs">
            <summary>
            Socket接收数据对象
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs.ClientSocket">
            <summary>
            引发事件的SocketClient对象
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs.ServerSocket">
            <summary>
            引发事件的SocketServer对象,如果我还iclient短，则为null
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs.RecvDataBuffer">
            <summary>
            接收数据
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs.SessionId">
            <summary>
            连接ID
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.TCPSocketEventArgs.ID">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.Async.UDPSocketEventArgs">
            <summary>
            UDP数据接收事件
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.UDPSocketEventArgs.RomoteIPEndPort">
            <summary>
            远程IP和端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.UDPSocketEventArgs.RecvDataBuffer">
            <summary>
            接收数据
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.UDPSocketEventArgs.SessionId">
            <summary>
            连接ID
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.Async.UDPSocketEventArgs.ID">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient">
            <summary>
            Socket客户端类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.ServerIP">
            <summary>
            服务端IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.ServerPort">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Connected">
            <summary>
            Socket连接状态
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.SendTimeOut">
            <summary>
            发送超时，默认1000ms
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Init(System.String,System.Int32)">
            <summary>
            初始化Socket连接
            </summary>
            <param name="ipAddr">IP地址</param>
            <param name="port">端口号</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Connect(System.String,System.Int32)">
            <summary>
            初始化Socket连接
            </summary>
            <param name="ipAddr">IP地址</param>
            <param name="port">端口号</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Connect">
            <summary>
            初始化Socket连接
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.ConnectCallback(System.IAsyncResult)">
            <summary>
            异步连接回调方法
            </summary>
            <param name="ar"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Receive">
            <summary>
            消息接收线程
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Send(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="data">数据</param>
            <returns>发送的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.SendAsync(System.Byte[])">
            <summary>
            异步发送数据
            </summary>
            <param name="data">数据</param>
            <returns>发送的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Disconnect">
            <summary>
            断开连接
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.Dispose">
            <summary>
            IDispose成员，断开连接
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.ClientConnected">
            <summary>
            连接成功
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.ClientDisconnected">
            <summary>
            连接断开
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketClient.DataReceived">
            <summary>
            接收到数据
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer">
            <summary>
            Socket服务端类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.SendTimeOut">
            <summary>
            发送超时，默认1000ms
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.IsStarted">
            <summary>
            服务的启用状态
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Listen(System.Int32,System.String)">
            <summary>
            初始化SocketServer，监听port，默认监听本机所有IP，可指定监听的IP地址
            </summary>
            <param name="port">端口号</param>
            <param name="ip">IP地址</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Listen(System.Int32[],System.String)">
            <summary>
            初始化SocketServer，监听多个port，默认监听本机所有IP，可指定监听的IP地址
            </summary>
            <param name="ports">端口号数组</param>
            <param name="ip">IP地址</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Listen(System.Net.Sockets.Socket)">
            <summary>
            负责监听连接的线程
            </summary>
            <param name="socketWatch"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Receive(HYC.HTCommunication.SocketWrapper.HTSocketConnection)">
            <summary>
            接收消息的线程
            </summary>
            <param name="connection"></param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.GetConnections">
            <summary>
            获取所有Socket连接
            </summary>
            <returns>所有Socket连接</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.GetConnections(System.String)">
            <summary>
            获取来自remoteIP的所有Socket连接
            </summary>
            <param name="remoteIP"></param>
            <returns>来自remoteIP的所有Socket连接</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.GetConnection(System.String,System.Int32)">
            <summary>
            获取指定的IP和Port的Socket连接
            </summary>
            <param name="remoteIP">远程IP</param>
            <param name="remotePort">远程端口</param>
            <returns>Socket连接</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.GetConnection(System.Net.EndPoint)">
            <summary>
            获取指定的EndPoint的Socket连接
            </summary>
            <param name="remoteEndPoint">远程客户端终结点</param>
            <returns>Socket连接</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Send(System.String,System.Int32,System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="remoteIP">远程客户端IP</param>
            <param name="remotePort">远程客户端Port</param>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Send(System.Net.EndPoint,System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="ep">远程客户端终结点</param>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.Dispose">
            <summary>
            断开连接，释放资源
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.ClientConnected">
            <summary>
            客户端连接成功
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.ClientDisconnected">
            <summary>
            客户端连接断开
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMSocketServer.DataReceived">
            <summary>
            接收到数据
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient">
            <summary>
            Udp客户端
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.SendTimeOut">
            <summary>
            发送超时，默认1000ms
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.IsStarted">
            <summary>
            服务的启用状态
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.Listen(System.Int32,System.String)">
            <summary>
            初始化，监听port，默认监听本机所有IP，可指定监听的IP地址
            </summary>
            <param name="port">端口号</param>
            <param name="ip">IP地址</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.Receive">
            <summary>
            消息接收线程
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.Send(System.Int32,System.Byte[])">
            <summary>
            发送数据（广播）
            </summary>
            <param name="remotePort">远程客户端Port</param>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.Send(System.String,System.Int32,System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="remoteIP">远程客户端IP</param>
            <param name="remotePort">远程客户端Port</param>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.Dispose">
            <summary>
            断开连接，释放资源
            </summary>
        </member>
        <member name="E:HYC.HTCommunication.SocketWrapper.HTCOMMUdpClient.DataReceived">
            <summary>
            接收到数据
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.HTSocketConnection">
            <summary>
            与本地监听服务相关的Socket连接类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.LocalIP">
            <summary>
            Socket本地IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.LocalPort">
            <summary>
            Socket本地端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.RemoteIP">
            <summary>
            远程客户端的IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.RemotePort">
            <summary>
            远程客户端的端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.Socket">
            <summary>
            套接字
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketConnection.Connected">
            <summary>
            Socket连接状态
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketConnection.#ctor(System.Net.Sockets.Socket)">
            <summary>
            构造函数
            </summary>
            <param name="socket">已建立连接的Socket</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketConnection.Send(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketConnection.SendAsync(System.Byte[])">
            <summary>
            异步发送数据
            </summary>
            <param name="data">数据</param>
            <returns>发送到Socket的字节数</returns>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketConnection.Dispose">
            <summary>
            断开连接，释放资源
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.HTSocketInfo">
            <summary>
            Socket连接参数类
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketInfo.IP">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.HTSocketInfo.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketInfo.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.HTSocketInfo.#ctor(System.String,System.Int32)">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="T:HYC.HTCommunication.SocketWrapper.SocketEventArgs">
            <summary>
            Socket事件参数
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.Socket">
            <summary>
            引发事件的Socket对象
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.LocalIP">
            <summary>
            Socket本地IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.LocalPort">
            <summary>
            Socket本地端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.RemoteIP">
            <summary>
            Socket远程主机IP地址
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.RemotePort">
            <summary>
            Socket远程主机端口
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.Datas">
            <summary>
            接收到的数据，在Connected事件和Disconnected事件引发时为null
            </summary>
        </member>
        <member name="P:HYC.HTCommunication.SocketWrapper.SocketEventArgs.UserData">
            <summary>
            用户数据
            </summary>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.SocketEventArgs.#ctor(System.Net.Sockets.Socket,System.Byte[])">
            <summary>
            构造函数
            </summary>
            <param name="socket">引发事件的Socket对象</param>
            <param name="datas">数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.SocketEventArgs.#ctor(System.Net.Sockets.Socket,System.Byte[],System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="socket">引发事件的Socket对象</param>
            <param name="datas">数据</param>
            <param name="userData">用户数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.SocketEventArgs.#ctor(HYC.HTCommunication.SocketWrapper.HTSocketConnection,System.Byte[],System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="connection">Socket服务端接收到的连接</param>
            <param name="datas">数据</param>
            <param name="userData">用户数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.SocketEventArgs.#ctor(System.Net.EndPoint,System.Byte[])">
            <summary>
            构造函数
            </summary>
            <param name="remoteEndPoint">远程终结点</param>
            <param name="datas">数据</param>
        </member>
        <member name="M:HYC.HTCommunication.SocketWrapper.SocketEventArgs.#ctor(System.Net.EndPoint,System.Byte[],System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="remoteEndPoint">远程终结点</param>
            <param name="datas">数据</param>
            <param name="userData">用户数据</param>
        </member>
    </members>
</doc>
