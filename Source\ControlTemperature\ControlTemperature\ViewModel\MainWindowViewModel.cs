﻿using ControlTemperature.Helper;
using ControlTemperature.Communication;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace ControlTemperature.ViewModel
{
    public class MainWindowViewModel : NotifyPropertyBase
    {
        private DispatcherTimer _updateTimer;
        private bool _isConnected = false;
        private TcpJsonServer _tcpServer;
        private bool _isTcpServerRunning = false;
        private string _tcpServerStatus = "未启动";
        private const int TcpServerPort = 8888;
        private LanguageInfo _selectedLanguage;

        public MainWindowViewModel()
        {
            // 初始化语言服务
            InitializeLanguageService();

            // 记录系统启动
            LogHelp.System(LanguageService.Instance.GetString("Log_SystemStartup", "TEC控制系统启动"), Helper.LogLevel.Info);

            // 初始化 Global 实例以加载配置
            new Gobal();
            LogHelp.System("配置文件加载完成", Helper.LogLevel.Info);

            // 初始化控制器集合
            InitializeControllers();
            LogHelp.System($"初始化了 {ControllerCount} 个控制器", Helper.LogLevel.Info);

            // 初始化最后更新时间
            LastUpdateTime = DateTime.Now;

            // 初始化命令
            InitializeCommands();

            // 连接TEC驱动器
            ConnectToTecDriversAsync();

            // 启动定时器更新数据
            StartUpdateTimer();
            LogHelp.System("数据更新定时器已启动", Helper.LogLevel.Info);

            // 启动TCP服务器
            StartTcpServer();
            
            // 延迟更新一次连接状态（确保UI正确显示）
            System.Threading.Tasks.Task.Delay(100).ContinueWith(_ => 
            {
                SafeInvokeOnUIThread(() =>
                {
                    foreach (var controller in Controllers)
                    {
                        controller.UpdateConnectionStatus();
                    }
                });
            });
            
            LogHelp.System("TEC控制系统初始化完成", Helper.LogLevel.Info);
        }

        /// <summary>
        /// 连接设备命令
        /// </summary>
        public ICommand ConnectCommand { get; private set; }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        public ICommand DisconnectCommand { get; private set; }

        /// <summary>
        /// 初始化语言服务
        /// </summary>
        private void InitializeLanguageService()
        {
            // 订阅语言变更事件
            LanguageService.Instance.LanguageChanged += OnLanguageChanged;

            // 设置当前选中的语言
            var currentLanguage = LanguageService.Instance.SupportedLanguages
                .FirstOrDefault(l => l.Code == LanguageService.Instance.CurrentCulture?.Name);
            _selectedLanguage = currentLanguage ?? LanguageService.Instance.SupportedLanguages.First();
        }

        /// <summary>
        /// 语言变更事件处理
        /// </summary>
        private void OnLanguageChanged(object sender, EventArgs e)
        {
            // 通知所有绑定的属性更新
            RaisePropertyChanged(nameof(WindowTitle));
            RaisePropertyChanged(nameof(ConnectDeviceText));
            RaisePropertyChanged(nameof(DisconnectDeviceText));
            RaisePropertyChanged(nameof(ConnectionStatusText));
            RaisePropertyChanged(nameof(ActiveDevicesText));
            RaisePropertyChanged(nameof(TcpServerText));
            RaisePropertyChanged(nameof(SystemStatusText));
            RaisePropertyChanged(nameof(CommunicationStatusText));
            RaisePropertyChanged(nameof(LanguageText));
            RaisePropertyChanged(nameof(LastUpdateTimeString));

            // 更新TCP服务器状态文本
            UpdateTcpServerStatusText();

            // 通知所有控制器更新语言
            foreach (var controller in Controllers)
            {
                controller.OnLanguageChanged();
            }
        }

        /// <summary>
        /// 更新TCP服务器状态文本
        /// </summary>
        private void UpdateTcpServerStatusText()
        {
            if (_isTcpServerRunning)
            {
                TcpServerStatus = LanguageService.Instance.GetFormattedString("Tcp_ServerStarted", TcpServerPort);
            }
            else
            {
                TcpServerStatus = LanguageService.Instance.GetString("Status_NotStarted", "未启动");
            }
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            ConnectCommand = new RelayCommand(() => ConnectToTecDriversAsync());
            DisconnectCommand = new RelayCommand(() => DisconnectTecDrivers());
        }

        /// <summary>
        /// 安全地在UI线程上调用操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private void SafeInvokeOnUIThread(Action action)
        {
            try
            {
                if (System.Windows.Application.Current?.Dispatcher != null)
                {
                    if (System.Windows.Application.Current.Dispatcher.CheckAccess())
                    {
                        // 已经在UI线程上，直接执行
                        action();
                    }
                    else
                    {
                        // 在UI线程上异步执行
                        System.Windows.Application.Current.Dispatcher.BeginInvoke(
                            System.Windows.Threading.DispatcherPriority.Background,
                            action);
                    }
                }
                else
                {
                    // 如果没有UI线程上下文，直接执行（通常在测试环境中）
                    action();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UI线程调用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 控制器数量
        /// </summary>
        public int ControllerCount
        {
            get => Gobal.TecCount;
            set
            {
                Gobal.TecCount = value;
                RaisePropertyChanged();
                InitializeControllers();
            }
        }

        private DateTime lastUpdateTime = DateTime.Now;
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => lastUpdateTime;
            set
            {
                lastUpdateTime = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 格式化的最后更新时间字符串
        /// </summary>
        public string LastUpdateTimeString => $"{LanguageService.Instance.GetString("MainWindow_LastUpdate", "最后更新:")} {LastUpdateTime:yyyy-MM-dd HH:mm:ss}";

        /// <summary>
        /// 窗口标题
        /// </summary>
        public string WindowTitle => LanguageService.Instance.GetString("MainWindow_Title", "温控平台控制系统");

        /// <summary>
        /// 连接设备按钮文本
        /// </summary>
        public string ConnectDeviceText => LanguageService.Instance.GetString("MainWindow_ConnectDevice", "连接设备");

        /// <summary>
        /// 断开连接按钮文本
        /// </summary>
        public string DisconnectDeviceText => LanguageService.Instance.GetString("MainWindow_DisconnectDevice", "断开连接");

        /// <summary>
        /// 连接状态文本
        /// </summary>
        public string ConnectionStatusText => LanguageService.Instance.GetString("MainWindow_ConnectionStatus", "连接状态:");

        /// <summary>
        /// 活动设备文本
        /// </summary>
        public string ActiveDevicesText => LanguageService.Instance.GetString("MainWindow_ActiveDevices", "活动设备:");

        /// <summary>
        /// TCP服务器文本
        /// </summary>
        public string TcpServerText => LanguageService.Instance.GetString("MainWindow_TcpServer", "TCP服务器:");

        /// <summary>
        /// 系统状态文本
        /// </summary>
        public string SystemStatusText => LanguageService.Instance.GetString("MainWindow_SystemStatus", "系统状态: 正常运行");

        /// <summary>
        /// 通讯状态文本
        /// </summary>
        public string CommunicationStatusText => LanguageService.Instance.GetString("MainWindow_CommunicationStatus", "通讯状态: 正常");

        /// <summary>
        /// 语言文本
        /// </summary>
        public string LanguageText => LanguageService.Instance.GetString("MainWindow_Language", "语言:");

        /// <summary>
        /// 支持的语言列表
        /// </summary>
        public List<LanguageInfo> SupportedLanguages => LanguageService.Instance.SupportedLanguages;

        /// <summary>
        /// 选中的语言
        /// </summary>
        public LanguageInfo SelectedLanguage
        {
            get => _selectedLanguage;
            set
            {
                if (_selectedLanguage != value)
                {
                    _selectedLanguage = value;
                    RaisePropertyChanged();
                    if (value != null)
                    {
                        LanguageService.Instance.SetLanguage(value.Code);
                    }
                }
            }
        }

        /// <summary>
        /// 控制器集合
        /// </summary>
        public ObservableCollection<ControllerControlViewModel> Controllers { get; set; }

        private int _selectedControllerIndex = 0;
        /// <summary>
        /// 选中的控制器索引（默认选中第一个）
        /// </summary>
        public int SelectedControllerIndex
        {
            get => _selectedControllerIndex;
            set
            {
                _selectedControllerIndex = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 是否已连接到TEC驱动器
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            set
            {
                _isConnected = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// TCP服务器是否正在运行
        /// </summary>
        public bool IsTcpServerRunning
        {
            get => _isTcpServerRunning;
            set
            {
                _isTcpServerRunning = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// TCP服务器状态
        /// </summary>
        public string TcpServerStatus
        {
            get => _tcpServerStatus;
            set
            {
                _tcpServerStatus = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 初始化控制器
        /// </summary>
        private void InitializeControllers()
        {
            Controllers = new ObservableCollection<ControllerControlViewModel>();

            for (int a = 1; a <= ControllerCount; a++)
            {
                var controller = new ControllerControlViewModel(a - 1); // 传递驱动器索引
                // 不再覆盖配置数据，让构造函数处理所有初始化
                Controllers.Add(controller);
            }
            
            RaisePropertyChanged(nameof(Controllers));
        }

        /// <summary>
        /// 异步连接到TEC驱动器
        /// </summary>
        private async void ConnectToTecDriversAsync()
        {
            try
            {
                await Task.Run(() => ConnectToTecDrivers());
                
                // 无论连接是否成功，都更新所有控制器的连接状态
                foreach (var controller in Controllers)
                {
                    controller.UpdateConnectionStatus();
                }
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"连接TEC驱动器时发生异常：{ex.Message}");
                LogHelp.Error($"连接TEC驱动器时发生异常：{ex.Message}");
                IsConnected = false;
                
                // 连接失败时也要更新状态
                foreach (var controller in Controllers)
                {
                    controller.UpdateConnectionStatus();
                }
            }
        }

        /// <summary>
        /// 连接到TEC驱动器（同步方法，供异步调用）
        /// </summary>
        private void ConnectToTecDrivers()
        {
            bool success = Gobal.modbusTec.ConnectTecDrivers();
            IsConnected = success;
            
            LogHelp.TecCommunication($"TEC驱动器连接{(success ? "成功" : "失败")}");
            LogHelp.System($"TEC驱动器连接{(success ? "成功" : "失败")}", success ? Helper.LogLevel.Info : Helper.LogLevel.Warning);
        }

        /// <summary>
        /// 启动定时器更新数据
        /// </summary>
        private void StartUpdateTimer()
        {
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1) // 保持1秒更新频率以确保实时性
            };
            
            _updateTimer.Tick += UpdateTimer_Tick;
            _updateTimer.Start();
        }

        /// <summary>
        /// 定时器更新事件
        /// </summary>
        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            if (IsConnected)
            {
                // 更新最后更新时间
                LastUpdateTime = DateTime.Now;
                
                // 异步更新所有控制器的数据，避免阻塞UI线程
                Task.Run(() =>
                {
                    foreach (var controller in Controllers)
                    {
                        controller.UpdateData();
                    }
                });
            }
        }

        /// <summary>
        /// 断开TEC驱动器连接
        /// </summary>
        private void DisconnectTecDrivers()
        {
            try
            {
                // 停止定时器
                _updateTimer?.Stop();
                
                // 断开所有驱动器连接
                Gobal.modbusTec.DisconnectTecDrivers();
                
                // 更新连接状态
                IsConnected = false;
                
                // 更新所有控制器的连接状态
                foreach (var controller in Controllers)
                {
                    controller.IsConnected = false;
                }
                
                LogHelp.TecCommunication("已断开所有TEC驱动器连接");
                LogHelp.System("已断开所有TEC驱动器连接", Helper.LogLevel.Info);
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"断开TEC驱动器连接时发生异常：{ex.Message}");
                LogHelp.Error($"断开TEC驱动器连接时发生异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 启动TCP服务器
        /// </summary>
        private void StartTcpServer()
        {
            try
            {
                _tcpServer = new TcpJsonServer(TcpServerPort, Gobal.modbusTec);
                _tcpServer.OnMessage += TcpServer_OnMessage;
                _tcpServer.Start();
                
                IsTcpServerRunning = true;
                TcpServerStatus = $"已启动（端口：{TcpServerPort}）";
                LogHelp.TcpCommunication($"TCP服务器已启动，端口：{TcpServerPort}");
                LogHelp.System($"TCP服务器已启动，端口：{TcpServerPort}", Helper.LogLevel.Info);
            }
            catch (Exception ex)
            {
                IsTcpServerRunning = false;
                TcpServerStatus = $"启动失败：{ex.Message}";
                LogHelp.TcpCommunication($"TCP服务器启动失败：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.Error($"TCP服务器启动失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 停止TCP服务器
        /// </summary>
        private void StopTcpServer()
        {
            try
            {
                _tcpServer?.Stop();
                _tcpServer = null;
                
                IsTcpServerRunning = false;
                TcpServerStatus = "已停止";
                LogHelp.TcpCommunication("TCP服务器已停止");
                LogHelp.System("TCP服务器已停止", Helper.LogLevel.Info);
            }
            catch (Exception ex)
            {
                LogHelp.TcpCommunication($"停止TCP服务器失败：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.Error($"停止TCP服务器失败：{ex.Message}");
            }
        }

        /// <summary>
        /// TCP服务器消息处理
        /// </summary>
        private void TcpServer_OnMessage(object sender, string message)
        {
            SafeInvokeOnUIThread(() =>
            {
                LogHelp.TcpCommunication($"TCP服务器消息：{message}", Helper.LogLevel.Debug);
            });
        }

        /// <summary>
        /// 停止定时器（在窗口关闭时调用）
        /// </summary>
        public void StopUpdateTimer()
        {
            _updateTimer?.Stop();
            _updateTimer = null;
            LogHelp.System("数据更新定时器已停止", Helper.LogLevel.Info);
            
            // 停止TCP服务器
            StopTcpServer();
            
            LogHelp.System("TEC控制系统已关闭", Helper.LogLevel.Info);
        }
    }
}