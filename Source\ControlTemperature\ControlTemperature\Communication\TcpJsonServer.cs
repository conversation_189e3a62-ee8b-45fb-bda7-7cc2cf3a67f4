using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ControlTemperature.Helper;

namespace ControlTemperature.Communication
{
    /// <summary>
    /// TCP JSON服务器，用于接收线体软件的JSON命令
    /// </summary>
    public class TcpJsonServer
    {
        private TcpListener _listener;
        private bool _isRunning;
        private readonly int _port;
        private readonly List<TcpClient> _clients;
        private readonly ModbusCommon _modbusCommon;
        private readonly object _lockObj = new object();

        public event EventHandler<string> OnMessage;

        public TcpJsonServer(int port, ModbusCommon modbusCommon)
        {
            _port = port;
            _modbusCommon = modbusCommon;
            _clients = new List<TcpClient>();
        }

        /// <summary>
        /// 启动TCP服务器
        /// </summary>
        public void Start()
        {
            try
            {
                _listener = new TcpListener(IPAddress.Any, _port);
                _listener.Start();
                _isRunning = true;

                OnMessage?.Invoke(this, $"TCP服务器已启动，监听端口：{_port}");
                LogHelp.TcpCommunication($"TCP服务器已启动，监听端口：{_port}");

                // 开始接受客户端连接
                Task.Run(() => AcceptClientsAsync());
            }
            catch (Exception ex)
            {
                OnMessage?.Invoke(this, $"启动TCP服务器失败：{ex.Message}");
                LogHelp.TcpCommunication($"启动TCP服务器失败：{ex.Message}", Helper.LogLevel.Error);
            }
        }

        /// <summary>
        /// 停止TCP服务器
        /// </summary>
        public void Stop()
        {
            _isRunning = false;

            try
            {
                // 关闭所有客户端连接
                lock (_lockObj)
                {
                    foreach (var client in _clients)
                    {
                        try
                        {
                            client?.Close();
                        }
                        catch { }
                    }
                    _clients.Clear();
                }

                _listener?.Stop();
                OnMessage?.Invoke(this, "TCP服务器已停止");
                LogHelp.TcpCommunication("TCP服务器已停止");
            }
            catch (Exception ex)
            {
                OnMessage?.Invoke(this, $"停止TCP服务器失败：{ex.Message}");
                LogHelp.TcpCommunication($"停止TCP服务器失败：{ex.Message}", Helper.LogLevel.Error);
            }
        }

        /// <summary>
        /// 异步接受客户端连接
        /// </summary>
        private async Task AcceptClientsAsync()
        {
            while (_isRunning)
            {
                try
                {
                    var client = await _listener.AcceptTcpClientAsync();
                    
                    lock (_lockObj)
                    {
                        _clients.Add(client);
                    }

                    var endpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    OnMessage?.Invoke(this, $"客户端已连接：{endpoint}");
                    LogHelp.TcpCommunication($"客户端已连接：{endpoint}");

                    // 为每个客户端创建处理任务
                    Task.Run(() => HandleClientAsync(client));
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        OnMessage?.Invoke(this, $"接受客户端连接失败：{ex.Message}");
                        LogHelp.TcpCommunication($"接受客户端连接失败：{ex.Message}", Helper.LogLevel.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 处理客户端消息
        /// </summary>
        private async Task HandleClientAsync(TcpClient client)
        {
            var endpoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
            NetworkStream stream = null;

            try
            {
                stream = client.GetStream();
                var buffer = new byte[4096];

                while (_isRunning && client.Connected)
                {
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0)
                        break;

                    string message = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    OnMessage?.Invoke(this, $"收到来自 {endpoint} 的消息：{message}");
                    LogHelp.TcpCommunication($"收到来自 {endpoint} 的消息：{message}", Helper.LogLevel.Debug);

                    // 处理JSON消息
                    await ProcessJsonMessageAsync(message, stream);
                }
            }
            catch (Exception ex)
            {
                OnMessage?.Invoke(this, $"处理客户端 {endpoint} 消息失败：{ex.Message}");
                LogHelp.TcpCommunication($"处理客户端 {endpoint} 消息失败：{ex.Message}", Helper.LogLevel.Error);
            }
            finally
            {
                // 清理资源
                try
                {
                    stream?.Close();
                    client?.Close();
                }
                catch { }

                lock (_lockObj)
                {
                    _clients.Remove(client);
                }

                OnMessage?.Invoke(this, $"客户端 {endpoint} 已断开连接");
                LogHelp.TcpCommunication($"客户端 {endpoint} 已断开连接");
            }
        }

        /// <summary>
        /// 处理JSON消息
        /// </summary>
        private async Task ProcessJsonMessageAsync(string jsonMessage, NetworkStream stream)
        {
            try
            {
                var request = JsonConvert.DeserializeObject<JsonRequest>(jsonMessage);
                var response = await ProcessCommandAsync(request);

                // 发送响应
                string responseJson = JsonConvert.SerializeObject(response);
                byte[] responseBytes = Encoding.UTF8.GetBytes(responseJson);
                await stream.WriteAsync(responseBytes, 0, responseBytes.Length);

                OnMessage?.Invoke(this, $"已发送响应：{responseJson}");
                LogHelp.TcpCommunication($"已发送响应：{responseJson}", Helper.LogLevel.Debug);
            }
            catch (Exception ex)
            {
                // 发送错误响应
                var errorResponse = new JsonResponse
                {
                    Result = "error",
                    Message = $"处理消息失败：{ex.Message}",
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                string errorJson = JsonConvert.SerializeObject(errorResponse);
                byte[] errorBytes = Encoding.UTF8.GetBytes(errorJson);
                await stream.WriteAsync(errorBytes, 0, errorBytes.Length);

                OnMessage?.Invoke(this, $"发送错误响应：{errorJson}");
                LogHelp.TcpCommunication($"发送错误响应：{errorJson}", Helper.LogLevel.Warning);
            }
        }

        /// <summary>
        /// 处理具体的JSON命令
        /// </summary>
        private async Task<JsonResponse> ProcessCommandAsync(JsonRequest request)
        {
            try
            {
                // 验证基本参数
                if (request.Unit < 1 || request.Unit > 8)
                {
                    return new JsonResponse
                    {
                        Result = "error",
                        Message = "控制器编号超出范围（1-8）",
                        Unit = request.Unit,
                        Channel = request.Channel,
                        Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                }

                // 检查控制器连接状态
                int driverIndex = request.Unit - 1;
                if (!_modbusCommon.IsDriverConnected(driverIndex))
                {
                    return new JsonResponse
                    {
                        Result = "error",
                        Message = $"控制器 {request.Unit} 未连接",
                        Unit = request.Unit,
                        Channel = request.Channel,
                        Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                }

                // 获取从站地址
                byte slaveId = Gobal.FileData.TecRTUDataLoad()[driverIndex].SlaveId;

                // 根据命令类型处理
                switch (request.Type?.ToLower())
                {
                    case "ambienttemp":
                        return await ProcessAmbientTempAsync(request, driverIndex, slaveId);

                    case "settargettemp":
                        return await ProcessSetTargetTempAsync(request, driverIndex, slaveId);

                    case "enable":
                        return await ProcessEnableAsync(request, driverIndex, slaveId);

                    case "setmaxtemp":
                        return await ProcessSetMaxTempAsync(request, driverIndex, slaveId);

                    case "setmintemp":
                        return await ProcessSetMinTempAsync(request, driverIndex, slaveId);

                    case "getcurrenttemp":
                        return await ProcessGetCurrentTempAsync(request, driverIndex, slaveId);

                    case "getallstatus":
                        return await ProcessGetAllStatusAsync(request, driverIndex, slaveId);

                    default:
                        return new JsonResponse
                        {
                            Result = "error",
                            Message = $"未知的命令类型：{request.Type}",
                            Unit = request.Unit,
                            Channel = request.Channel,
                            Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        };
                }
            }
            catch (Exception ex)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = $"处理命令失败：{ex.Message}",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        /// <summary>
        /// 处理环境温度设置
        /// </summary>
        private async Task<JsonResponse> ProcessAmbientTempAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            if (!request.Value.HasValue)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "缺少温度值参数",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            bool success = _modbusCommon.SetChannelExternalTemperature(driverIndex, slaveId, request.Channel, (float)request.Value.Value);

            return new JsonResponse
            {
                Result = success ? "success" : "error",
                Message = success ? "环境温度设置成功" : "环境温度设置失败",
                Unit = request.Unit,
                Channel = request.Channel,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 处理设置目标温度
        /// </summary>
        private async Task<JsonResponse> ProcessSetTargetTempAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            if (!request.Value.HasValue)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "缺少温度值参数",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            bool success = _modbusCommon.SetChannelTargetTemperature(driverIndex, slaveId, request.Channel, (float)request.Value.Value);

            return new JsonResponse
            {
                Result = success ? "success" : "error",
                Message = success ? "目标温度设置成功" : "目标温度设置失败",
                Unit = request.Unit,
                Channel = request.Channel,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 处理使能控制
        /// </summary>
        private async Task<JsonResponse> ProcessEnableAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            if (!request.Value.HasValue)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "缺少使能状态参数",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            bool enable = request.Value.Value != 0;
            bool success = _modbusCommon.SetChannelEnable(driverIndex, slaveId, request.Channel, enable);

            return new JsonResponse
            {
                Result = success ? "success" : "error",
                Message = success ? (enable ? "TEC已开启" : "TEC已关闭") : "TEC使能设置失败",
                Unit = request.Unit,
                Channel = request.Channel,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 处理设置最大温度
        /// </summary>
        private async Task<JsonResponse> ProcessSetMaxTempAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            if (!request.Value.HasValue)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "缺少温度值参数",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            bool success = _modbusCommon.SetChannelMaxTemperature(driverIndex, slaveId, request.Channel, (float)request.Value.Value);

            return new JsonResponse
            {
                Result = success ? "success" : "error",
                Message = success ? "最大温度设置成功" : "最大温度设置失败",
                Unit = request.Unit,
                Channel = request.Channel,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 处理设置最小温度
        /// </summary>
        private async Task<JsonResponse> ProcessSetMinTempAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            if (!request.Value.HasValue)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "缺少温度值参数",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            bool success = _modbusCommon.SetChannelMinTemperature(driverIndex, slaveId, request.Channel, (float)request.Value.Value);

            return new JsonResponse
            {
                Result = success ? "success" : "error",
                Message = success ? "最小温度设置成功" : "最小温度设置失败",
                Unit = request.Unit,
                Channel = request.Channel,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 处理获取当前温度
        /// </summary>
        private async Task<JsonResponse> ProcessGetCurrentTempAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            if (request.Channel < 1 || request.Channel > 2)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "通道编号超出范围（1-2）",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            float? currentTemp = _modbusCommon.GetChannelCurrentTemperature(driverIndex, slaveId, request.Channel);
            float? targetTemp = _modbusCommon.GetChannelTargetTemperature(driverIndex, slaveId, request.Channel);
            bool? enabled = _modbusCommon.GetChannelEnable(driverIndex, slaveId, request.Channel);

            if (currentTemp.HasValue && targetTemp.HasValue && enabled.HasValue)
            {
                return new JsonResponse
                {
                    Result = "success",
                    Data = new
                    {
                        currentTemp = Math.Round(currentTemp.Value, 3),
                        targetTemp = Math.Round(targetTemp.Value, 3),
                        enabled = enabled.Value
                    },
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            else
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = "读取温度数据失败",
                    Unit = request.Unit,
                    Channel = request.Channel,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        /// <summary>
        /// 处理获取所有状态
        /// </summary>
        private async Task<JsonResponse> ProcessGetAllStatusAsync(JsonRequest request, int driverIndex, byte slaveId)
        {
            try
            {
                var channelData = new Dictionary<string, object>();

                for (int channel = 1; channel <= 2; channel++)
                {
                    float? currentTemp = _modbusCommon.GetChannelCurrentTemperature(driverIndex, slaveId, channel);
                    float? targetTemp = _modbusCommon.GetChannelTargetTemperature(driverIndex, slaveId, channel);
                    bool? enabled = _modbusCommon.GetChannelEnable(driverIndex, slaveId, channel);
                    float? ambientTemp = _modbusCommon.GetChannelExternalTemperature(driverIndex, slaveId, channel);

                    channelData[$"channel{channel}"] = new
                    {
                        currentTemp = currentTemp.HasValue ? Math.Round(currentTemp.Value, 3) : (double?)null,
                        targetTemp = targetTemp.HasValue ? Math.Round(targetTemp.Value, 3) : (double?)null,
                        enabled = enabled ?? false,
                        ambientTemp = ambientTemp.HasValue ? Math.Round(ambientTemp.Value, 3) : (double?)null
                    };
                }

                return new JsonResponse
                {
                    Result = "success",
                    Data = channelData,
                    Unit = request.Unit,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                return new JsonResponse
                {
                    Result = "error",
                    Message = $"读取状态失败：{ex.Message}",
                    Unit = request.Unit,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }
    }

    /// <summary>
    /// JSON请求数据结构
    /// </summary>
    public class JsonRequest
    {
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("unit")]
        public int Unit { get; set; }

        [JsonProperty("channel")]
        public int Channel { get; set; }

        [JsonProperty("value")]
        public double? Value { get; set; }

        [JsonProperty("timestamp")]
        public string Timestamp { get; set; }
    }

    /// <summary>
    /// JSON响应数据结构
    /// </summary>
    public class JsonResponse
    {
        [JsonProperty("result")]
        public string Result { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public object Data { get; set; }

        [JsonProperty("unit")]
        public int Unit { get; set; }

        [JsonProperty("channel")]
        public int Channel { get; set; }

        [JsonProperty("timestamp")]
        public string Timestamp { get; set; }
    }
} 