# TEC JSON通信协议文档

## 概述
本协议定义了线体软件（客户端）与TEC控制软件（服务器）之间的JSON通信格式。TEC控制软件作为TCP服务器监听连接，线体软件作为客户端连接并发送JSON命令。

## 系统架构
- **线体软件（客户端）**：通过红外传感器采集温度，通过TCP发送JSON命令
- **TEC控制软件（服务器）**：接收JSON命令
- **支持**：最多8个控制器，每个控制器2个通道

## 协议格式

### 1. 请求格式（线体软件 → TEC控制软件）

所有请求统一使用以下格式：
```json
{
    "type": "命令类型",
    "unit": 控制器编号,
    "channel": 通道编号,
    "value": 数值参数,
    "timestamp": "时间戳"
}
```

#### 字段说明：
- `type`: 命令类型（必须）
- `unit`: 控制器编号，范围1-8（必须）
- `channel`: 通道编号，范围1-2（部分命令需要）
- `value`: 数值参数（部分命令需要）
- `timestamp`: 时间戳，格式"YYYY-MM-DD HH:mm:ss"（可选）

### 2. 具体命令

#### 2.1 环境温度数据推送
```json
{
    "type": "ambientTemp",
    "unit": 1,
    "channel": 1,
    "value": 22.3,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 2.2 设置目标温度
```json
{
    "type": "setTargetTemp",
    "unit": 1,
    "channel": 1,
    "value": 25.5
}
```

#### 2.3 开关使能控制
```json
{
    "type": "enable",
    "unit": 1,
    "channel": 1,
    "value": true
}
```

#### 2.4 设置温度上限
```json
{
    "type": "setMaxTemp",
    "unit": 1,
    "channel": 1,
    "value": 85.0
}
```

#### 2.5 设置温度下限
```json
{
    "type": "setMinTemp",
    "unit": 1,
    "channel": 1,
    "value": -20.0
}
```

#### 2.6 读取实时温度
```json
{
    "type": "getCurrentTemp",
    "unit": 1,
    "channel": 1
}
```

#### 2.7 读取所有状态
```json
{
    "type": "getAllStatus",
    "unit": 1
}
```

### 3. 响应格式（TEC控制软件 → 线体软件）

#### 3.1 操作结果响应
```json
{
    "result": "success",
    "message": "操作成功",
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 3.2 错误响应
```json
{
    "result": "error",
    "message": "错误信息",
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 3.3 实时温度响应
```json
{
    "result": "success",
    "data": {
        "currentTemp": 25.3,
        "targetTemp": 25.0,
        "enabled": true
    },
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

#### 3.4 所有状态响应
```json
{
    "result": "success",
    "data": {
        "channel1": {
            "currentTemp": 25.3,
            "targetTemp": 25.0,
            "enabled": true,
            "ambientTemp": 22.1
        },
        "channel2": {
            "currentTemp": 30.2,
            "targetTemp": 30.0,
            "enabled": false,
            "ambientTemp": 22.1
        }
    },
    "unit": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

## 通信示例

### 示例1：设置控制器1通道1的环境温度
**线体软件发送:**
```json
{
    "type": "ambientTemp",
    "unit": 1,
    "channel": 1,
    "value": 22.3,
    "timestamp": "2024-01-15 10:30:15"
}
```

**TEC控制软件响应:**
```json
{
    "result": "success",
    "message": "环境温度设置成功",
    "unit": 1,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

### 示例2：开启控制器2通道1
**线体软件发送:**
```json
{
    "type": "enable",
    "unit": 2,
    "channel": 1,
    "value": true
}
```

**TEC控制软件响应:**
```json
{
    "result": "success",
    "message": "TEC已开启",
    "unit": 2,
    "channel": 1,
    "timestamp": "2024-01-15 10:30:15"
}
```

### 示例3：设置控制器1通道2目标温度
**线体软件发送:**
```json
{
    "type": "setTargetTemp",
    "unit": 1,
    "channel": 2,
    "value": 28.5
}
```

**TEC控制软件响应:**
```json
{
    "result": "success",
    "message": "目标温度设置成功",
    "unit": 1,
    "channel": 2,
    "timestamp": "2024-01-15 10:30:15"
}
```

## 地址映射

### Modbus地址对应关系：
- **40032 (地址31)**: 通道1外部温度输入
- **40034 (地址33)**: 通道2外部温度输入

### 各控制器地址计算：
- 控制器Unit的地址 = 基础地址 + (Unit-1) * 地址偏移

## 连接参数

- **协议**: TCP
- **端口**: 8888（默认）
- **编码**: UTF-8
- **超时**: 5秒

## 注意事项

1. 所有温度单位均为摄氏度（℃）
2. 温度精度保留3位小数
3. 时间格式为：YYYY-MM-DD HH:mm:ss
4. 字段名称大小写敏感
5. JSON格式必须符合标准规范
6. 控制器编号范围：1-8
7. 通道编号范围：1-2
8. 必须先连接成功才能发送命令
9. 建议添加心跳检测机制 