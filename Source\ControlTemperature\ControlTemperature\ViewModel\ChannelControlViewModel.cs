﻿using ControlTemperature.Helper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ControlTemperature.ViewModel
{
    /// <summary>
    /// 通道的控制视图模型
    /// </summary>
    public class ChannelControlViewModel : NotifyPropertyBase
    {
        private int _driverIndex = -1;
        private int _channelNumber = 1;
        private TecModbusData _connectionData;

        /// <summary>
        /// 应用温度设置命令（目标温度、上下限）
        /// </summary>
        public ICommand ApplyTemperatureSettingsCommand { get; private set; }

        /// <summary>
        /// 急停命令
        /// </summary>
        public ICommand EmergencyStopCommand { get; private set; }

        /// <summary>
        /// 清空日志命令
        /// </summary>
        public ICommand ClearLogCommand { get; private set; }

        public ChannelControlViewModel()
        {
            InitializeCommands();
            InitializeLog();
            
            // 订阅LogHelp的日志事件
            LogHelp.LogEvent += OnLogEvent;
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            ApplyTemperatureSettingsCommand = new RelayCommand(() => ApplyTemperatureSettingsAsync());
            EmergencyStopCommand = new RelayCommand(() => EmergencyStop());
            ClearLogCommand = new RelayCommand(() => ClearLog());
        }

        /// <summary>
        /// 初始化日志
        /// </summary>
        private void InitializeLog()
        {
            LogEntries = new ObservableCollection<LogEntry>();
            LogMessages = "系统启动...\n";
            
            // 延迟添加初始化消息，确保UI线程已经准备就绪
            Task.Delay(100).ContinueWith(_ => 
            {
                AddLogMessage("日志系统已初始化");
            });
        }

        private string headerColor;
        /// <summary>
        /// 标题颜色
        /// </summary>
        public string HeaderColor
        {
            get => headerColor;
            set
            {
                headerColor = value;
                RaisePropertyChanged();
            }
        }

        private string channelName;
        /// <summary>
        /// 通道名称
        /// </summary>
        public string ChannelName
        {
            get => channelName;
            set
            {
                channelName = value;
                RaisePropertyChanged();
            }
        }

        private string channelNumber;
        /// <summary>
        /// 通道编号
        /// </summary>
        public string ChannelNumber
        {
            get => channelNumber;
            set
            {
                channelNumber = value;
                RaisePropertyChanged();
            }
        }

        private double currentTemperature = 25.3;
        /// <summary>
        /// 当前温度
        /// </summary>
        public double CurrentTemperature
        {
            get => currentTemperature;
            set
            {
                currentTemperature = value;
                RaisePropertyChanged();
            }
        }

        private double targetTemperature = 25.0;
        /// <summary>
        /// 目标温度（从设备读取的实际值）
        /// </summary>
        public double TargetTemperature
        {
            get => targetTemperature;
            set
            {
                targetTemperature = value;
                RaisePropertyChanged();
            }
        }

        private double targetTemperatureSetting = 25.0;
        /// <summary>
        /// 目标温度设置值（用于用户输入）
        /// </summary>
        public double TargetTemperatureSetting
        {
            get => targetTemperatureSetting;
            set
            {
                targetTemperatureSetting = value;
                RaisePropertyChanged();
            }
        }

        private bool isEnabled = false;
        private bool isEnableOperationInProgress = false;
        private DateTime lastEnableOperationTime = DateTime.MinValue;
        private const int ENABLE_OPERATION_DEBOUNCE_MS = 300; // 防抖延迟300毫秒
        
        private bool isTemperatureSettingInProgress = false;
        private bool isAmbientTemperatureSettingInProgress = false;
        
        /// <summary>
        /// 使能状态
        /// </summary>
        public bool IsEnabled
        {
            get => isEnabled;
            set
            {
                // 立即更新UI状态，提供即时反馈
                var oldValue = isEnabled;
                isEnabled = value;
                RaisePropertyChanged();

                // 异步执行设备通信，避免阻塞UI线程
                if (_driverIndex >= 0 && _connectionData != null)
                {
                    // 防抖逻辑：如果操作正在进行中或距离上次操作时间太短，则取消之前的操作
                    var currentTime = DateTime.Now;
                    if (isEnableOperationInProgress || 
                        (currentTime - lastEnableOperationTime).TotalMilliseconds < ENABLE_OPERATION_DEBOUNCE_MS)
                    {
                        // 延迟执行，避免连续点击
                        Task.Delay(ENABLE_OPERATION_DEBOUNCE_MS).ContinueWith(t => 
                        {
                            if (isEnabled == value && !isEnableOperationInProgress) // 确保状态没有再次变化
                            {
                                SetChannelEnableAsync(value);
                            }
                        });
                    }
                    else
                    {
                        SetChannelEnableAsync(value);
                    }
                }
            }
        }

        /// <summary>
        /// 使能操作是否正在进行中
        /// </summary>
        public bool IsEnableOperationInProgress
        {
            get => isEnableOperationInProgress;
            private set
            {
                isEnableOperationInProgress = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 温度设置操作是否正在进行中
        /// </summary>
        public bool IsTemperatureSettingInProgress
        {
            get => isTemperatureSettingInProgress;
            private set
            {
                isTemperatureSettingInProgress = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 环境温度设置操作是否正在进行中
        /// </summary>
        public bool IsAmbientTemperatureSettingInProgress
        {
            get => isAmbientTemperatureSettingInProgress;
            private set
            {
                isAmbientTemperatureSettingInProgress = value;
                RaisePropertyChanged();
            }
        }

        private double maxTemperature = 85.0;
        /// <summary>
        /// 最高温度限制（从设备读取的实际值）
        /// </summary>
        public double MaxTemperature
        {
            get => maxTemperature;
            set
            {
                maxTemperature = value;
                RaisePropertyChanged();
            }
        }

        private double maxTemperatureSetting = 85.0;
        /// <summary>
        /// 最高温度设置值（用于用户输入）
        /// </summary>
        public double MaxTemperatureSetting
        {
            get => maxTemperatureSetting;
            set
            {
                maxTemperatureSetting = value;
                RaisePropertyChanged();
            }
        }

        private double minTemperature = -20.0;
        /// <summary>
        /// 最低温度限制（从设备读取的实际值）
        /// </summary>
        public double MinTemperature
        {
            get => minTemperature;
            set
            {
                minTemperature = value;
                RaisePropertyChanged();
            }
        }

        private double minTemperatureSetting = -20.0;
        /// <summary>
        /// 最低温度设置值（用于用户输入）
        /// </summary>
        public double MinTemperatureSetting
        {
            get => minTemperatureSetting;
            set
            {
                minTemperatureSetting = value;
                RaisePropertyChanged();
            }
        }

        private double ambientTemperature = 25.0;
        /// <summary>
        /// 环境温度
        /// </summary>
        public double AmbientTemperature
        {
            get => ambientTemperature;
            set
            {
                ambientTemperature = value;
                RaisePropertyChanged();
            }
        }

        private bool isHeating = false;
        /// <summary>
        /// 是否在加热（true=加热，false=制冷）
        /// </summary>
        public bool IsHeating
        {
            get => isHeating;
            set
            {
                isHeating = value;
                RaisePropertyChanged();
            }
        }

        private bool fanRunning = false;
        /// <summary>
        /// 风扇是否运行
        /// </summary>
        public bool FanRunning
        {
            get => fanRunning;
            set
            {
                fanRunning = value;
                RaisePropertyChanged();
            }
        }

        private string logMessages = "";
        /// <summary>
        /// 日志消息
        /// </summary>
        public string LogMessages
        {
            get => logMessages;
            set
            {
                logMessages = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 日志条目集合（用于高效显示）
        /// </summary>
        public ObservableCollection<LogEntry> LogEntries { get; private set; }

        /// <summary>
        /// 是否使用高效日志显示
        /// </summary>
        public bool UseEfficientLogging { get; set; } = true;

        private readonly object _logLock = new object();
        private const int MAX_LOG_LINES = 100; // 最大日志行数，防止内存过大

        /// <summary>
        /// 安全地在UI线程上调用操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private void SafeInvokeOnUIThread(Action action)
        {
            try
            {
                if (System.Windows.Application.Current?.Dispatcher != null)
                {
                    if (System.Windows.Application.Current.Dispatcher.CheckAccess())
                    {
                        // 已经在UI线程上，直接执行
                        action();
                    }
                    else
                    {
                        // 在UI线程上异步执行
                        System.Windows.Application.Current.Dispatcher.BeginInvoke(
                            System.Windows.Threading.DispatcherPriority.Background,
                            action);
                    }
                }
                else
                {
                    // 如果没有UI线程上下文，直接执行（通常在测试环境中）
                    action();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UI线程调用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置驱动器信息
        /// </summary>
        /// <param name="driverIndex">驱动器索引</param>
        /// <param name="channelNumber">通道号</param>
        public void SetDriverInfo(int driverIndex, int channelNumber)
        {
            _driverIndex = driverIndex;
            _channelNumber = channelNumber;

            // 获取连接配置数据
            var connectionDataArray = Gobal.FileData.TecRTUDataLoad();
            if (connectionDataArray != null && driverIndex < connectionDataArray.Length)
            {
                _connectionData = connectionDataArray[driverIndex];
                AddLogMessage($"通道 {_channelNumber} 初始化完成，端口：{_connectionData.Port}", Helper.LogLevel.Info);
            }
            else
            {
                AddLogMessage($"通道 {_channelNumber} 配置加载失败", Helper.LogLevel.Error);
            }
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        public void UpdateData()
        {
            if (_driverIndex < 0 || _connectionData == null)
                return;

            // 检查控制器是否已连接
            if (!Gobal.modbusTec.IsDriverConnected(_driverIndex))
                return;

            try
            {
                // 批量读取数据以减少通信次数
                var updates = new List<Action>();

                // 更新当前温度
                var currentTemp = Gobal.modbusTec.GetChannelCurrentTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (currentTemp.HasValue)
                {
                    var newTemp = Math.Round(currentTemp.Value, 3);
                    if (Math.Abs(CurrentTemperature - newTemp) > 0.001) // 只有变化时才更新
                    {
                        updates.Add(() => CurrentTemperature = newTemp);
                    }
                }

                // 更新目标温度
                var targetTemp = Gobal.modbusTec.GetChannelTargetTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (targetTemp.HasValue)
                {
                    var newTargetTemp = Math.Round(targetTemp.Value, 3);
                    if (Math.Abs(TargetTemperature - newTargetTemp) > 0.001) // 只有变化时才更新
                    {
                        updates.Add(() => TargetTemperature = newTargetTemp);
                    }
                }

                // 更新使能状态
                var enableStatus = Gobal.modbusTec.GetChannelEnable(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (enableStatus.HasValue && enableStatus.Value != isEnabled)
                {
                    updates.Add(() => {
                        isEnabled = enableStatus.Value; // 直接设置字段，避免触发命令
                        RaisePropertyChanged(nameof(IsEnabled));
                    });
                }

                // 更新运行状态
                var runningState = Gobal.modbusTec.GetChannelRunningState(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (runningState.HasValue && runningState.Value != IsHeating)
                {
                    updates.Add(() => IsHeating = runningState.Value);
                }

                // 更新风扇状态
                var fanState = Gobal.modbusTec.GetChannelFanState(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (fanState.HasValue && fanState.Value != FanRunning)
                {
                    updates.Add(() => FanRunning = fanState.Value);
                }

                // 更新外部温度
                var externalTemp = Gobal.modbusTec.GetChannelExternalTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (externalTemp.HasValue)
                {
                    var newAmbientTemp = Math.Round(externalTemp.Value, 3);
                    if (Math.Abs(AmbientTemperature - newAmbientTemp) > 0.001) // 只有变化时才更新
                    {
                        updates.Add(() => AmbientTemperature = newAmbientTemp);
                    }
                }

                // 批量更新UI属性，减少UI刷新次数
                if (updates.Count > 0)
                {
                    // 安全地调用UI更新
                    SafeInvokeOnUIThread(() =>
                    {
                        foreach (var update in updates)
                        {
                            update();
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"数据更新失败：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.TecCommunication($"更新通道 {_channelNumber} 数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化温度上下限（首次连接时调用）
        /// </summary>
        public void InitializeTemperatureLimits()
        {
            if (_driverIndex < 0 || _connectionData == null)
                return;

            try
            {
                // 读取温度上限
                var highLimit = Gobal.modbusTec.GetTemperatureHighLimit(_driverIndex, _connectionData.SlaveId);
                if (highLimit.HasValue)
                {
                    MaxTemperature = Math.Round(highLimit.Value, 3);
                    MaxTemperatureSetting = MaxTemperature; // 同步到设置属性
                }

                // 读取温度下限
                var lowLimit = Gobal.modbusTec.GetTemperatureLowLimit(_driverIndex, _connectionData.SlaveId);
                if (lowLimit.HasValue)
                {
                    MinTemperature = Math.Round(lowLimit.Value, 3);
                    MinTemperatureSetting = MinTemperature; // 同步到设置属性
                }

                // 读取目标温度
                var targetTemp = Gobal.modbusTec.GetChannelTargetTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber);
                if (targetTemp.HasValue)
                {
                    TargetTemperature = Math.Round(targetTemp.Value, 3);
                    TargetTemperatureSetting = TargetTemperature; // 同步到设置属性
                }
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"初始化通道 {_channelNumber} 温度上下限失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 异步设置温度上限
        /// </summary>
        /// <param name="highLimit">温度上限</param>
        public async void SetTemperatureHighLimitAsync(double highLimit)
        {
            if (_driverIndex < 0 || _connectionData == null)
                return;

            try
            {
                await Task.Run(() => SetTemperatureHighLimit(highLimit));
                MaxTemperature = highLimit;
                LogHelp.TecCommunication($"控制器 {_driverIndex + 1} 温度上限设置为：{highLimit}℃");
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"设置控制器 {_driverIndex + 1} 温度上限异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 设置温度上限（同步方法，供异步调用）
        /// </summary>
        /// <param name="highLimit">温度上限</param>
        private void SetTemperatureHighLimit(double highLimit)
        {
            bool success = Gobal.modbusTec.SetTemperatureHighLimit(_driverIndex, _connectionData.SlaveId, (float)highLimit);
            if (!success)
            {
                throw new Exception($"设置温度上限失败");
            }
        }

        /// <summary>
        /// 异步设置温度下限
        /// </summary>
        /// <param name="lowLimit">温度下限</param>
        public async void SetTemperatureLowLimitAsync(double lowLimit)
        {
            if (_driverIndex < 0 || _connectionData == null)
                return;

            try
            {
                await Task.Run(() => SetTemperatureLowLimit(lowLimit));
                MinTemperature = lowLimit;
                LogHelp.TecCommunication($"控制器 {_driverIndex + 1} 温度下限设置为：{lowLimit}℃");
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"设置控制器 {_driverIndex + 1} 温度下限异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 设置温度下限（同步方法，供异步调用）
        /// </summary>
        /// <param name="lowLimit">温度下限</param>
        private void SetTemperatureLowLimit(double lowLimit)
        {
            bool success = Gobal.modbusTec.SetTemperatureLowLimit(_driverIndex, _connectionData.SlaveId, (float)lowLimit);
            if (!success)
            {
                throw new Exception($"设置温度下限失败");
            }
        }

        /// <summary>
        /// 设置通道使能状态（同步方法，供异步调用）
        /// </summary>
        /// <param name="enable">使能状态</param>
        private void SetChannelEnable(bool enable)
        {
            bool success = Gobal.modbusTec.SetChannelEnable(_driverIndex, _connectionData.SlaveId, _channelNumber, enable);
            if (!success)
            {
                // 如果设置失败，恢复UI状态
                SafeInvokeOnUIThread(() =>
                {
                    isEnabled = !enable; // 恢复原来的状态
                    RaisePropertyChanged(nameof(IsEnabled));
                });
                
                throw new Exception($"设置通道 {_channelNumber} 使能状态失败");
            }
        }

        /// <summary>
        /// 异步设置通道使能状态
        /// </summary>
        /// <param name="enable">使能状态</param>
        private async void SetChannelEnableAsync(bool enable)
        {
            try
            {
                IsEnableOperationInProgress = true;
                await Task.Run(() => SetChannelEnable(enable));
                AddLogMessage($"使能状态设置为：{(enable ? "开启" : "关闭")}", Helper.LogLevel.Info);
                LogHelp.TecCommunication($"通道 {_channelNumber} 使能状态设置为：{(enable ? "开启" : "关闭")}");
            }
            catch (Exception ex)
            {
                AddLogMessage($"设置使能状态异常：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.TecCommunication($"设置通道 {_channelNumber} 使能状态异常：{ex.Message}");
            }
            finally
            {
                IsEnableOperationInProgress = false;
                lastEnableOperationTime = DateTime.Now; // 记录操作时间
            }
        }

        /// <summary>
        /// 异步应用温度设置（目标温度、上下限）
        /// </summary>
        public async void ApplyTemperatureSettingsAsync()
        {
            if (IsTemperatureSettingInProgress)
                return;

            if (_driverIndex < 0 || _connectionData == null)
            {
                AddLogMessage("设备未连接，无法设置温度", Helper.LogLevel.Warning);
                return;
            }

            try
            {
                IsTemperatureSettingInProgress = true;
                await Task.Run(() => ApplyTemperatureSettings());
                AddLogMessage($"温度设置完成", Helper.LogLevel.Info);
            }
            catch (Exception ex)
            {
                AddLogMessage($"温度设置异常：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.TecCommunication($"设置通道 {_channelNumber} 温度异常：{ex.Message}");
            }
            finally
            {
                IsTemperatureSettingInProgress = false;
            }
        }

        /// <summary>
        /// 应用温度设置（同步方法，供异步调用）
        /// </summary>
        private void ApplyTemperatureSettings()
        {
            // 设置目标温度
            bool targetSuccess = Gobal.modbusTec.SetChannelTargetTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber, (float)TargetTemperatureSetting);
            if (!targetSuccess)
            {
                throw new Exception($"设置目标温度失败");
            }
            
            AddLogMessage($"目标温度设置为：{TargetTemperatureSetting:F3}℃", Helper.LogLevel.Info);
            LogHelp.TecCommunication($"通道 {_channelNumber} 目标温度设置为：{TargetTemperatureSetting}℃");

            // 设置温度上限
            bool maxSuccess = Gobal.modbusTec.SetTemperatureHighLimit(_driverIndex, _connectionData.SlaveId, (float)MaxTemperatureSetting);
            if (!maxSuccess)
            {
                throw new Exception($"设置温度上限失败");
            }
            
            AddLogMessage($"温度上限设置为：{MaxTemperatureSetting:F3}℃", Helper.LogLevel.Info);
            LogHelp.TecCommunication($"通道 {_channelNumber} 温度上限设置为：{MaxTemperatureSetting}℃");

            // 设置温度下限
            bool minSuccess = Gobal.modbusTec.SetTemperatureLowLimit(_driverIndex, _connectionData.SlaveId, (float)MinTemperatureSetting);
            if (!minSuccess)
            {
                throw new Exception($"设置温度下限失败");
            }
            
            AddLogMessage($"温度下限设置为：{MinTemperatureSetting:F3}℃", Helper.LogLevel.Info);
            LogHelp.TecCommunication($"通道 {_channelNumber} 温度下限设置为：{MinTemperatureSetting}℃");
        }

        /// <summary>
        /// 异步设置环境温度
        /// </summary>
        /// <param name="temperature">环境温度</param>
        public async void SetAmbientTemperatureAsync(double temperature)
        {
            if (IsAmbientTemperatureSettingInProgress)
                return;

            if (_driverIndex < 0 || _connectionData == null)
            {
                AddLogMessage("设备未连接，无法设置环境温度", Helper.LogLevel.Warning);
                return;
            }

            try
            {
                IsAmbientTemperatureSettingInProgress = true;
                await Task.Run(() => SetAmbientTemperature(temperature));
                AmbientTemperature = temperature;
                AddLogMessage($"环境温度设置为：{temperature:F3}℃", Helper.LogLevel.Info);
                LogHelp.TecCommunication($"通道 {_channelNumber} 环境温度设置为：{temperature}℃");
            }
            catch (Exception ex)
            {
                AddLogMessage($"设置环境温度异常：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.TecCommunication($"设置通道 {_channelNumber} 环境温度异常：{ex.Message}");
            }
            finally
            {
                IsAmbientTemperatureSettingInProgress = false;
            }
        }

        /// <summary>
        /// 设置环境温度（同步方法，供异步调用）
        /// </summary>
        /// <param name="temperature">环境温度</param>
        private void SetAmbientTemperature(double temperature)
        {
            bool success = Gobal.modbusTec.SetChannelExternalTemperature(_driverIndex, _connectionData.SlaveId, _channelNumber, (float)temperature);
            if (!success)
            {
                throw new Exception($"设置环境温度失败");
            }
        }

        /// <summary>
        /// 急停操作
        /// </summary>
        private void EmergencyStop()
        {
            try
            {
                // 立即关闭使能
                IsEnabled = false;
                AddLogMessage($"急停操作执行，已关闭使能", Helper.LogLevel.Warning);
                LogHelp.TecCommunication($"通道 {_channelNumber} 急停操作执行，已关闭使能");
            }
            catch (Exception ex)
            {
                AddLogMessage($"急停操作异常：{ex.Message}", Helper.LogLevel.Error);
                LogHelp.TecCommunication($"通道 {_channelNumber} 急停操作异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        public void AddLogMessage(string message, Helper.LogLevel level = Helper.LogLevel.Info)
        {
            // 检查参数有效性
            if (string.IsNullOrEmpty(message))
                return;

            // 使用低优先级异步处理日志，避免阻塞UI线程
            Task.Run(() =>
            {
                try
                {
                    var timestamp = DateTime.Now.ToString("HH:mm:ss");
                    var levelText = LogHelp.GetLogLevelText(level);
                    var logEntryText = $"[{timestamp}] {levelText} {message}\n";

                    // 安全地调用UI更新
                    SafeInvokeOnUIThread(() =>
                    {
                        UpdateLogEntries(message, level, logEntryText);
                    });
                }
                catch (Exception ex)
                {
                    // 日志记录失败时，避免死循环
                    System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 更新日志条目（UI线程安全）
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="level">日志级别</param>
        /// <param name="logEntryText">格式化的日志文本</param>
        private void UpdateLogEntries(string message, Helper.LogLevel level, string logEntryText)
        {
            try
            {
                lock (_logLock)
                {
                    // 确保LogEntries已初始化
                    if (LogEntries == null)
                    {
                        LogEntries = new ObservableCollection<LogEntry>();
                    }

                    // 添加到高效日志集合
                    var logEntry = new LogEntry(message, level);
                    LogEntries.Add(logEntry);
                    
                    // 限制日志条目数量，防止内存过大
                    if (LogEntries.Count > MAX_LOG_LINES)
                    {
                        var removeCount = LogEntries.Count - MAX_LOG_LINES;
                        for (int i = 0; i < removeCount; i++)
                        {
                            LogEntries.RemoveAt(0);
                        }
                    }

                    // 更新字符串格式
                    LogMessages += logEntryText;

                    // 限制日志行数，防止内存过大
                    var lines = LogMessages.Split('\n');
                    if (lines.Length > MAX_LOG_LINES)
                    {
                        var keepLines = lines.Skip(lines.Length - MAX_LOG_LINES).ToArray();
                        LogMessages = string.Join("\n", keepLines);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新日志条目失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理LogHelp日志事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">日志事件参数</param>
        private void OnLogEvent(object sender, LogEventArgs e)
        {
            // 只显示与当前通道相关的日志，或者系统级别的日志
            // 添加级别过滤，减少不必要的日志显示
            if ((e.Category.Contains("TCP通信") || e.Category.Contains("系统") || 
                e.Category.Contains("设备操作") || e.Category.Contains("TEC通信")) &&
                (e.Level == Helper.LogLevel.Info || e.Level == Helper.LogLevel.Warning || e.Level == Helper.LogLevel.Error))
            {
                // 异步添加日志，避免阻塞事件处理
                Task.Run(() => AddLogMessage($"[{e.Category}] {e.Message}", e.Level));
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLog()
        {
            lock (_logLock)
            {
                LogMessages = "";
                AddLogMessage("日志已清空");
            }
        }


    }
}
