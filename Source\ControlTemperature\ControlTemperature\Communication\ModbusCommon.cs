﻿using ControlTemperature.Helper;
using HYC.HTCommunication.SerialPortWrapper;
using HYC.HTModbus;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ControlTemperature.Communication
{
    public class ModbusCommon
    {
        private ModbusMaster[] masters = new ModbusMaster[Gobal.TecCount];
        private TecModbusData[] connectData = new TecModbusData[Gobal.TecCount];
        private bool[] connectionStatus = new bool[Gobal.TecCount]; // 跟踪每个控制器的连接状态

        public ModbusCommon()
        {
            connectData = Gobal.FileData.TecRTUDataLoad();
        }

        /// <summary>
        /// 连接iTC6022Cv5驱动板的方法
        /// </summary>
        /// <returns>是否至少有一个控制器连接成功</returns>
        public bool ConnectTecDrivers()
        {
            int successCount = 0;

            for (int o = 0; o < Gobal.TecCount; o++)
            {
                connectionStatus[o] = false; // 初始化为未连接状态

                try
                {
                    // 创建串口连接信息
                    var serialPortInfo = new HTSerialPortInfo
                    {
                        PortName = connectData[o].Port,          // 串口名称，如 "COM1"
                        BaudRate = connectData[o].BaudRate,      // 波特率：115200（根据文档7.1 Modbus参数）
                        DataBits = 8,                            // 数据位：8bit
                        StopBits = StopBits.One,                 // 停止位：1
                        Parity = Parity.None                     // 校验位：N（无校验）
                    };

                    // 创建Modbus RTU实例
                    masters[o] = ModbusMaster.CreateRTU(serialPortInfo);

                    // 设置通信超时时间（可根据需要调整）
                    masters[o].TimeOut = 1000;

                    // 设置字节序为Big-Endian（库的要求，但实际浮点数使用CDAB格式）
                    masters[o].IsBigEndian = true;

                    // 连接设备
                    masters[o].Connect();
                    if (connectData[o].Port == "COM6")
                    {

                    }

                    // 测试连接 - 读取离散输入状态
                    bool[] status = masters[o].ReadInputs(connectData[o].SlaveId, 0, 6);

                    // 连接成功
                    connectionStatus[o] = true;
                    successCount++;
                    LogHelp.TecCommunication($"TEC驱动器 {o + 1} 连接成功，串口：{connectData[o].Port}，从站地址：{connectData[o].SlaveId}");
                    LogHelp.System($"TEC驱动器 {o + 1} 连接成功", Helper.LogLevel.Info);
                }
                catch (Exception ex)
                {
                    // 单个控制器连接失败，记录日志但不影响其他控制器
                    connectionStatus[o] = false;
                    if (masters[o] != null)
                    {
                        masters[o].Dispose();
                        masters[o] = null;
                    }
                    LogHelp.TecCommunication($"TEC驱动器 {o + 1} 连接失败，串口：{connectData[o].Port}，错误：{ex.Message}");
                    LogHelp.Error($"TEC驱动器 {o + 1} 连接失败：{ex.Message}");
                }
            }

            LogHelp.TecCommunication($"TEC驱动器连接完成，成功：{successCount}/{Gobal.TecCount}");
            return successCount > 0; // 至少有一个控制器连接成功就返回true
        }



        /// <summary>
        /// 获取TEC驱动器通道使能状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>使能状态：true=ON，false=OFF，null=读取失败</returns>
        public bool? GetChannelEnable(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 检查连接状态
                if (driverIndex >= connectionStatus.Length || !connectionStatus[driverIndex])
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，离散输入地址：10001（通道1），10002（通道2）
                // 库内部已处理前缀，直接使用相对地址：0（通道1），1（通道2）
                ushort inputAddress = (ushort)(channel - 1);

                // 读取离散输入（0x02功能码）
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, inputAddress, 1);

                if (result != null && result.Length > 0)
                {
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 使能状态：{(result[0] ? "ON" : "OFF")}");
                    return result[0];
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 使能状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取TEC驱动器所有通道使能状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>通道状态数组，索引0对应通道1，索引1对应通道2</returns>
        public bool[] GetAllChannelEnable(int driverIndex, byte slaveAddress)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 从离散输入地址10001开始读取2个通道状态
                // 库内部已处理前缀，直接使用相对地址0开始
                ushort startAddress = 0;

                // 读取2个离散输入
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, startAddress, 2);

                if (result != null && result.Length >= 2)
                {
                    for (int i = 0; i < 2; i++)
                    {
                        Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {i + 1} 使能状态：{(result[i] ? "ON" : "OFF")}");
                    }
                    return result;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 所有通道使能状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取TEC驱动器通道运行状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>运行状态：true=加热，false=制冷，null=读取失败</returns>
        public bool? GetChannelRunningState(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，离散输入地址：10003（通道1），10004（通道2）
                // 库内部已处理前缀，直接使用相对地址：2（通道1），3（通道2）
                ushort inputAddress = (ushort)(2 + (channel - 1));

                // 读取离散输入（0x02功能码）
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, inputAddress, 1);

                if (result != null && result.Length > 0)
                {
                    string stateText = result[0] ? "加热" : "制冷";
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 运行状态：{stateText}");
                    return result[0];
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 运行状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取TEC驱动器所有通道运行状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>运行状态数组，索引0对应通道1，索引1对应通道2（true=加热，false=制冷）</returns>
        public bool[] GetAllChannelRunningState(int driverIndex, byte slaveAddress)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 从离散输入地址10003开始读取2个通道运行状态
                // 库内部已处理前缀，直接使用相对地址2开始
                ushort startAddress = 2;

                // 读取2个离散输入
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, startAddress, 2);

                if (result != null && result.Length >= 2)
                {
                    for (int i = 0; i < 2; i++)
                    {
                        string stateText = result[i] ? "加热" : "制冷";
                        Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {i + 1} 运行状态：{stateText}");
                    }
                    return result;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 所有通道运行状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取TEC驱动器风扇运行状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>风扇状态：true=ON，false=OFF，null=读取失败</returns>
        public bool? GetChannelFanState(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，离散输入地址：10005（通道1），10006（通道2）
                // 库内部已处理前缀，直接使用相对地址：4（通道1），5（通道2）
                ushort inputAddress = (ushort)(4 + (channel - 1));

                // 读取离散输入（0x02功能码）
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, inputAddress, 1);

                if (result != null && result.Length > 0)
                {
                    string stateText = result[0] ? "ON" : "OFF";
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 风扇状态：{stateText}");
                    return result[0];
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 风扇状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取TEC驱动器所有通道风扇运行状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>风扇状态数组，索引0对应通道1，索引1对应通道2（true=ON，false=OFF）</returns>
        public bool[] GetAllChannelFanState(int driverIndex, byte slaveAddress)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 从离散输入地址10005开始读取2个通道风扇状态
                // 库内部已处理前缀，直接使用相对地址4开始
                ushort startAddress = 4;

                // 读取2个离散输入
                bool[] result = masters[driverIndex].ReadInputs(slaveAddress, startAddress, 2);

                if (result != null && result.Length >= 2)
                {
                    for (int i = 0; i < 2; i++)
                    {
                        string stateText = result[i] ? "ON" : "OFF";
                        Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {i + 1} 风扇状态：{stateText}");
                    }
                    return result;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 所有通道风扇状态失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取温度高限
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>温度高限值（℃），null表示读取失败</returns>
        public float? GetTemperatureHighLimit(int driverIndex, byte slaveAddress)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 保持寄存器地址：40001
                // 库内部已处理前缀，直接使用相对地址0
                ushort registerAddress = 0;

                // 读取2个保持寄存器（32位浮点数）
                ushort[] data = masters[driverIndex].ReadHoldingRegisters(slaveAddress, registerAddress, 2);

                if (data != null && data.Length >= 2)
                {
                    float temperature = UshortArrayToFloat(data);
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 温度高限：{temperature}℃");
                    return temperature;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 温度高限失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取温度低限
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>温度低限值（℃），null表示读取失败</returns>
        public float? GetTemperatureLowLimit(int driverIndex, byte slaveAddress)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                // 保持寄存器地址：40003
                // 库内部已处理前缀，直接使用相对地址2
                ushort registerAddress = 2;

                // 读取2个保持寄存器（32位浮点数）
                ushort[] data = masters[driverIndex].ReadHoldingRegisters(slaveAddress, registerAddress, 2);

                if (data != null && data.Length >= 2)
                {
                    float temperature = UshortArrayToFloat(data);
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 温度低限：{temperature}℃");
                    return temperature;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 温度低限失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取通道当前设置温度
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>设置温度值（℃），null表示读取失败</returns>
        public float? GetChannelTargetTemperature(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，保持寄存器地址：40005（通道1），40019（通道2）
                // 库内部已处理前缀，直接使用相对地址：4（通道1），18（通道2）
                ushort registerAddress = channel == 1 ? (ushort)4 : (ushort)18;

                // 读取2个保持寄存器（32位浮点数）
                ushort[] data = masters[driverIndex].ReadHoldingRegisters(slaveAddress, registerAddress, 2);

                if (data != null)
                {
                    float temperature = UshortArrayToFloat(data);
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 目标温度：{temperature}℃");
                    return temperature;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 目标温度失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取通道外部温度输入
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>外部温度值（℃），null表示读取失败</returns>
        public float? GetChannelExternalTemperature(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，保持寄存器地址：40032（通道1），40034（通道2）
                // 库内部已处理前缀，直接使用相对地址：31（通道1），33（通道2）
                ushort registerAddress = channel == 1 ? (ushort)31 : (ushort)33;

                // 读取2个保持寄存器（32位浮点数）
                ushort[] data = masters[driverIndex].ReadHoldingRegisters(slaveAddress, registerAddress, 2);

                if (data != null && data.Length >= 2)
                {
                    float temperature = UshortArrayToFloat(data);
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 外部温度输入：{temperature}℃");
                    return temperature;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 外部温度输入失败：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取通道当前实际温度（从输入寄存器读取）
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <returns>当前实际温度值（℃），null表示读取失败</returns>
        public float? GetChannelCurrentTemperature(int driverIndex, byte slaveAddress, int channel)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return null;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return null;
                }

                // 根据文档，输入寄存器地址：30001（通道1），30005（通道2）
                // 库内部已处理前缀，直接使用相对地址：0（通道1），4（通道2）
                ushort registerAddress = channel == 1 ? (ushort)0 : (ushort)4;

                // 读取2个输入寄存器（32位浮点数）
                ushort[] data = masters[driverIndex].ReadInputRegisters(slaveAddress, registerAddress, 2);

                if (data != null && data.Length >= 2)
                {
                    float temperature = UshortArrayToFloat(data);
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 当前实际温度：{temperature}℃");
                    return temperature;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取TEC驱动器 {driverIndex + 1} 通道 {channel} 当前实际温度失败：{ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// 设置TEC驱动器通道使能状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <param name="enable">使能状态：true=ON，false=OFF</param>
        /// <returns>操作是否成功</returns>
        public bool SetChannelEnable(int driverIndex, byte slaveAddress, int channel, bool enable)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return false;
                }

                // 根据文档，线圈地址：10001（通道1），10002（通道2）
                // 库内部已处理前缀，直接使用相对地址：0（通道1），1（通道2）
                ushort coilAddress = (ushort)(channel - 1);

                // 使用写单个线圈方法
                masters[driverIndex].WriteSingleCoil(slaveAddress, coilAddress, enable);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 使能状态设置为：{(enable ? "ON" : "OFF")}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 通道 {channel} 使能状态失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置TEC驱动器多个通道使能状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channelStates">通道状态数组，索引0对应通道1，索引1对应通道2</param>
        /// <returns>操作是否成功</returns>
        public bool SetMultipleChannelEnable(int driverIndex, byte slaveAddress, bool[] channelStates)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channelStates == null || channelStates.Length == 0 || channelStates.Length > 2)
                {
                    Console.WriteLine("通道状态数组无效，最多支持2个通道");
                    return false;
                }

                // 从线圈地址10001开始
                // 库内部已处理前缀，直接使用相对地址0开始
                ushort startAddress = 0;

                // 使用写多个线圈方法
                masters[driverIndex].WriteMultipleCoils(slaveAddress, startAddress, channelStates);

                for (int i = 0; i < channelStates.Length; i++)
                {
                    Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {i + 1} 使能状态设置为：{(channelStates[i] ? "ON" : "OFF")}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 多通道使能状态失败：{ex.Message}");
                return false;
            }
        }




        /// <summary>
        /// 设置通道温度高限
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="highLimit">温度高限值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetTemperatureHighLimit(int driverIndex, byte slaveAddress, float highLimit)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                // 根据文档，保持寄存器地址：40001（温度设置高限）
                // 库内部已处理前缀，直接使用相对地址0
                ushort registerAddress = 0;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(highLimit);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 温度高限设置为：{highLimit}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 温度高限失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置通道温度低限
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="lowLimit">温度低限值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetTemperatureLowLimit(int driverIndex, byte slaveAddress, float lowLimit)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                // 根据文档，保持寄存器地址：40003（温度设置低限）
                // 库内部已处理前缀，直接使用相对地址2
                ushort registerAddress = 2;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(lowLimit);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 温度低限设置为：{lowLimit}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 温度低限失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置通道当前设置温度
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <param name="temperature">设置温度值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetChannelTargetTemperature(int driverIndex, byte slaveAddress, int channel, float temperature)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return false;
                }

                // 根据文档，保持寄存器地址：40005（通道1），40019（通道2）
                // 库内部已处理前缀，直接使用相对地址：4（通道1），18（通道2）
                ushort registerAddress = channel == 1 ? (ushort)4 : (ushort)18;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(temperature);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 目标温度设置为：{temperature}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 通道 {channel} 目标温度失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置通道外部温度输入
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <param name="externalTemperature">外部温度值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetChannelExternalTemperature(int driverIndex, byte slaveAddress, int channel, float externalTemperature)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return false;
                }

                // 根据文档，保持寄存器地址：40032（通道1），40034（通道2）
                // 库内部已处理前缀，直接使用相对地址：31（通道1），33（通道2）
                ushort registerAddress = channel == 1 ? (ushort)31 : (ushort)33;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(externalTemperature);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 外部温度输入设置为：{externalTemperature}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 通道 {channel} 外部温度输入失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置通道最大温度限制
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <param name="maxTemperature">最大温度值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetChannelMaxTemperature(int driverIndex, byte slaveAddress, int channel, float maxTemperature)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return false;
                }

                // 使用全局温度高限设置（40001），因为这是设备级别的设置
                // 库内部已处理前缀，直接使用相对地址：0
                ushort registerAddress = 0;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(maxTemperature);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 最大温度设置为：{maxTemperature}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 通道 {channel} 最大温度失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置通道最小温度限制
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="channel">通道号（1或2）</param>
        /// <param name="minTemperature">最小温度值（℃）</param>
        /// <returns>操作是否成功</returns>
        public bool SetChannelMinTemperature(int driverIndex, byte slaveAddress, int channel, float minTemperature)
        {
            try
            {
                if (driverIndex < 0 || driverIndex >= masters.Length || masters[driverIndex] == null)
                {
                    Console.WriteLine($"驱动器索引 {driverIndex} 无效或未连接");
                    return false;
                }

                if (channel < 1 || channel > 2)
                {
                    Console.WriteLine($"通道号 {channel} 无效，只支持通道1和通道2");
                    return false;
                }

                // 使用全局温度低限设置（40003），因为这是设备级别的设置
                // 库内部已处理前缀，直接使用相对地址：2
                ushort registerAddress = 2;

                // 将float转换为ushort数组（32位浮点数，占用2个寄存器）
                ushort[] data = FloatToUshortArray(minTemperature);

                // 写入保持寄存器
                masters[driverIndex].WriteMultipleRegisters(slaveAddress, registerAddress, data);

                Console.WriteLine($"TEC驱动器 {driverIndex + 1} 通道 {channel} 最小温度设置为：{minTemperature}℃");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置TEC驱动器 {driverIndex + 1} 通道 {channel} 最小温度失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将float转换为ushort数组（用于Modbus传输）
        /// </summary>
        /// <param name="value">浮点数值</param>
        /// <returns>ushort数组</returns>
        private ushort[] FloatToUshortArray(float value)
        {
            byte[] bytes = BitConverter.GetBytes(value);

            // CDAB格式：将ABCD重排为CDAB
            // bytes[0]=A, bytes[1]=B, bytes[2]=C, bytes[3]=D
            ushort[] result = new ushort[2];
            result[0] = (ushort)(bytes[2] | (bytes[3] << 8)); // CD
            result[1] = (ushort)(bytes[0] | (bytes[1] << 8)); // AB

            return result;
        }



        /// <summary>
        /// 将ushort数组转换为float（用于Modbus接收）
        /// </summary>
        /// <param name="data">ushort数组</param>
        /// <returns>浮点数值</returns>
        private float UshortArrayToFloat(ushort[] data)
        {
            if (data == null || data.Length < 2)
                throw new ArgumentException("数据长度不足");



            // 尝试CDAB格式：data[0]=CD, data[1]=AB，需要重排为ABCD
            byte[] bytes = new byte[4];

            // 从data[1]获取AB字节
            bytes[0] = (byte)(data[1] & 0xFF);       // A
            bytes[1] = (byte)((data[1] >> 8) & 0xFF); // B

            // 从data[0]获取CD字节
            bytes[2] = (byte)(data[0] & 0xFF);       // C
            bytes[3] = (byte)((data[0] >> 8) & 0xFF); // D

            return BitConverter.ToSingle(bytes, 0);
        }



        /// <summary>
        /// 检查特定控制器的连接状态
        /// </summary>
        /// <param name="driverIndex">驱动器索引（0开始）</param>
        /// <returns>连接状态</returns>
        public bool IsDriverConnected(int driverIndex)
        {
            if (driverIndex < 0 || driverIndex >= connectionStatus.Length)
                return false;
            return connectionStatus[driverIndex];
        }

        /// <summary>
        /// 断开所有TEC驱动器连接
        /// </summary>
        public void DisconnectTecDrivers()
        {
            for (int v = 0; v < masters.Length; v++)
            {
                if (masters[v] != null)
                {
                    try
                    {
                        masters[v].Dispose();
                    }
                    catch (Exception ex)
                    {
                        LogHelp.TecCommunication($"断开TEC驱动器 {v + 1} 连接时出错：{ex.Message}");
                    }
                    finally
                    {
                        masters[v] = null;
                        connectionStatus[v] = false; // 重置连接状态
                    }
                }
            }
        }




    }
}
