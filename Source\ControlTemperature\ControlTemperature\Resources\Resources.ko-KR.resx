<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- 메인 윈도우 -->
  <data name="MainWindow_Title" xml:space="preserve">
    <value>온도 제어 플랫폼 시스템</value>
  </data>
  <data name="MainWindow_ConnectDevice" xml:space="preserve">
    <value>장치 연결</value>
  </data>
  <data name="MainWindow_DisconnectDevice" xml:space="preserve">
    <value>장치 연결 해제</value>
  </data>
  <data name="MainWindow_ConnectionStatus" xml:space="preserve">
    <value>연결 상태:</value>
  </data>
  <data name="MainWindow_ActiveDevices" xml:space="preserve">
    <value>활성 장치:</value>
  </data>
  <data name="MainWindow_TcpServer" xml:space="preserve">
    <value>TCP 서버:</value>
  </data>
  <data name="MainWindow_SystemStatus" xml:space="preserve">
    <value>시스템 상태: 정상 실행</value>
  </data>
  <data name="MainWindow_CommunicationStatus" xml:space="preserve">
    <value>통신 상태: 정상</value>
  </data>
  <data name="MainWindow_LastUpdate" xml:space="preserve">
    <value>마지막 업데이트:</value>
  </data>
  <data name="MainWindow_Language" xml:space="preserve">
    <value>언어:</value>
  </data>
  
  <!-- 컨트롤러 제어 -->
  <data name="Controller_SerialPort" xml:space="preserve">
    <value>시리얼 포트:</value>
  </data>
  <data name="Controller_DeviceStatus" xml:space="preserve">
    <value>장치 상태:</value>
  </data>
  <data name="Controller_CommunicationStatus" xml:space="preserve">
    <value>통신 상태:</value>
  </data>
  <data name="Controller_Header" xml:space="preserve">
    <value>컨트롤러{0} (주소:{1})</value>
  </data>
  
  <!-- 채널 제어 -->
  <data name="Channel_Enable" xml:space="preserve">
    <value>활성화</value>
  </data>
  <data name="Channel_EmergencyStop" xml:space="preserve">
    <value>비상정지</value>
  </data>
  <data name="Channel_CurrentTemperature" xml:space="preserve">
    <value>현재 온도</value>
  </data>
  <data name="Channel_TargetTemperature" xml:space="preserve">
    <value>목표 온도</value>
  </data>
  <data name="Channel_TemperatureSettings" xml:space="preserve">
    <value>온도 설정</value>
  </data>
  <data name="Channel_SetTemperature" xml:space="preserve">
    <value>설정 온도:</value>
  </data>
  <data name="Channel_MaxTemperature" xml:space="preserve">
    <value>최대 온도:</value>
  </data>
  <data name="Channel_MinTemperature" xml:space="preserve">
    <value>최소 온도:</value>
  </data>
  <data name="Channel_SetButton" xml:space="preserve">
    <value>설정</value>
  </data>
  <data name="Channel_OutputStatus" xml:space="preserve">
    <value>출력 상태</value>
  </data>
  <data name="Channel_RunningMode" xml:space="preserve">
    <value>실행 모드:</value>
  </data>
  <data name="Channel_FanStatus" xml:space="preserve">
    <value>팬 상태:</value>
  </data>
  <data name="Channel_RealtimeLog" xml:space="preserve">
    <value>실시간 로그</value>
  </data>
  <data name="Channel_ClearLog" xml:space="preserve">
    <value>지우기</value>
  </data>
  <data name="Channel_SettingEnableStatus" xml:space="preserve">
    <value>활성화 상태 설정 중...</value>
  </data>
  <data name="Channel_SettingTemperature" xml:space="preserve">
    <value>온도 설정 중...</value>
  </data>
  
  <!-- 상태 텍스트 -->
  <data name="Status_Connected" xml:space="preserve">
    <value>연결됨</value>
  </data>
  <data name="Status_Disconnected" xml:space="preserve">
    <value>연결 해제됨</value>
  </data>
  <data name="Status_Online" xml:space="preserve">
    <value>온라인</value>
  </data>
  <data name="Status_Offline" xml:space="preserve">
    <value>오프라인</value>
  </data>
  <data name="Status_Normal" xml:space="preserve">
    <value>정상</value>
  </data>
  <data name="Status_Unknown" xml:space="preserve">
    <value>알 수 없음</value>
  </data>
  <data name="Status_NotStarted" xml:space="preserve">
    <value>시작되지 않음</value>
  </data>
  <data name="Status_Heating" xml:space="preserve">
    <value>가열</value>
  </data>
  <data name="Status_Cooling" xml:space="preserve">
    <value>냉각</value>
  </data>
  <data name="Status_Running" xml:space="preserve">
    <value>실행 중</value>
  </data>
  <data name="Status_Stopped" xml:space="preserve">
    <value>정지됨</value>
  </data>
  <data name="Status_Enabled" xml:space="preserve">
    <value>활성화됨</value>
  </data>
  <data name="Status_Disabled" xml:space="preserve">
    <value>비활성화됨</value>
  </data>
  
  <!-- 언어 옵션 -->
  <data name="Language_Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Language_English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Language_Korean" xml:space="preserve">
    <value>한국어</value>
  </data>

  <!-- 로그 메시지 -->
  <data name="Log_SystemStartup" xml:space="preserve">
    <value>시스템 시작 중...</value>
  </data>
  <data name="Log_SystemInitialized" xml:space="preserve">
    <value>로그 시스템이 초기화되었습니다</value>
  </data>
  <data name="Log_ChannelInitialized" xml:space="preserve">
    <value>채널 {0} 초기화 완료, 포트: {1}</value>
  </data>
  <data name="Log_ChannelConfigLoadFailed" xml:space="preserve">
    <value>채널 {0} 구성 로드 실패</value>
  </data>
  <data name="Log_DataUpdateFailed" xml:space="preserve">
    <value>데이터 업데이트 실패: {0}</value>
  </data>
  <data name="Log_EnableStatusSet" xml:space="preserve">
    <value>활성화 상태 설정: {0}</value>
  </data>
  <data name="Log_EnableStatusSetFailed" xml:space="preserve">
    <value>활성화 상태 설정 실패: {0}</value>
  </data>
  <data name="Log_DeviceNotConnected" xml:space="preserve">
    <value>장치가 연결되지 않음, 온도를 설정할 수 없습니다</value>
  </data>
  <data name="Log_TemperatureSettingComplete" xml:space="preserve">
    <value>온도 설정 완료</value>
  </data>
  <data name="Log_TemperatureSettingFailed" xml:space="preserve">
    <value>온도 설정 실패: {0}</value>
  </data>
  <data name="Log_TargetTemperatureSet" xml:space="preserve">
    <value>목표 온도 설정: {0}℃</value>
  </data>
  <data name="Log_MaxTemperatureSet" xml:space="preserve">
    <value>최대 온도 설정: {0}℃</value>
  </data>
  <data name="Log_MinTemperatureSet" xml:space="preserve">
    <value>최소 온도 설정: {0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSet" xml:space="preserve">
    <value>환경 온도 설정: {0}℃</value>
  </data>
  <data name="Log_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>환경 온도 설정 실패: {0}</value>
  </data>
  <data name="Log_EmergencyStopExecuted" xml:space="preserve">
    <value>비상정지 실행됨, 활성화 비활성화됨</value>
  </data>
  <data name="Log_EmergencyStopFailed" xml:space="preserve">
    <value>비상정지 실패: {0}</value>
  </data>
  <data name="Log_LogCleared" xml:space="preserve">
    <value>로그가 지워졌습니다</value>
  </data>

  <!-- 오류 메시지 -->
  <data name="Error_SetTargetTemperatureFailed" xml:space="preserve">
    <value>목표 온도 설정 실패</value>
  </data>
  <data name="Error_SetMaxTemperatureFailed" xml:space="preserve">
    <value>최대 온도 설정 실패</value>
  </data>
  <data name="Error_SetMinTemperatureFailed" xml:space="preserve">
    <value>최소 온도 설정 실패</value>
  </data>
  <data name="Error_SetChannelEnableFailed" xml:space="preserve">
    <value>채널 {0} 활성화 상태 설정 실패</value>
  </data>
  <data name="Error_SetAmbientTemperatureFailed" xml:space="preserve">
    <value>환경 온도 설정 실패</value>
  </data>
  <data name="Error_UIThreadCallFailed" xml:space="preserve">
    <value>UI 스레드 호출 실패: {0}</value>
  </data>
  <data name="Error_LogRecordFailed" xml:space="preserve">
    <value>로그 기록 실패: {0}</value>
  </data>
  <data name="Error_UpdateLogEntryFailed" xml:space="preserve">
    <value>로그 항목 업데이트 실패: {0}</value>
  </data>

  <!-- TCP 서버 메시지 -->
  <data name="Tcp_ServerStarted" xml:space="preserve">
    <value>TCP 서버 시작됨, 포트 수신 대기: {0}</value>
  </data>
  <data name="Tcp_ServerStartFailed" xml:space="preserve">
    <value>TCP 서버 시작 실패: {0}</value>
  </data>
  <data name="Tcp_ServerStopped" xml:space="preserve">
    <value>TCP 서버 중지됨</value>
  </data>
  <data name="Tcp_ServerStopFailed" xml:space="preserve">
    <value>TCP 서버 중지 실패: {0}</value>
  </data>
  <data name="Tcp_ClientConnected" xml:space="preserve">
    <value>클라이언트 연결됨: {0}</value>
  </data>
  <data name="Tcp_ClientConnectionFailed" xml:space="preserve">
    <value>클라이언트 연결 수락 실패: {0}</value>
  </data>
  <data name="Tcp_MessageReceived" xml:space="preserve">
    <value>{0}에서 메시지 수신: {1}</value>
  </data>
  <data name="Tcp_ProcessMessageFailed" xml:space="preserve">
    <value>클라이언트 {0} 메시지 처리 실패: {1}</value>
  </data>
  <data name="Tcp_ClientDisconnected" xml:space="preserve">
    <value>클라이언트 {0} 연결 해제됨</value>
  </data>
  <data name="Tcp_ResponseSent" xml:space="preserve">
    <value>응답 전송됨: {0}</value>
  </data>
  <data name="Tcp_ProcessMessageFailedGeneral" xml:space="preserve">
    <value>메시지 처리 실패: {0}</value>
  </data>
  <data name="Tcp_ErrorResponseSent" xml:space="preserve">
    <value>오류 응답 전송됨: {0}</value>
  </data>
  <data name="Tcp_ControllerOutOfRange" xml:space="preserve">
    <value>컨트롤러 번호 범위 초과 (1-8)</value>
  </data>
  <data name="Tcp_ControllerNotConnected" xml:space="preserve">
    <value>컨트롤러 {0} 연결되지 않음</value>
  </data>
  <data name="Tcp_UnknownCommandType" xml:space="preserve">
    <value>알 수 없는 명령 유형: {0}</value>
  </data>
  <data name="Tcp_ProcessCommandFailed" xml:space="preserve">
    <value>명령 처리 실패: {0}</value>
  </data>
  <data name="Tcp_ChannelOutOfRange" xml:space="preserve">
    <value>채널 번호 범위 초과 (1-2)</value>
  </data>
  <data name="Tcp_MissingTemperatureValue" xml:space="preserve">
    <value>온도 값 매개변수 누락</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetSuccess" xml:space="preserve">
    <value>환경 온도 설정 성공</value>
  </data>
  <data name="Tcp_AmbientTemperatureSetFailed" xml:space="preserve">
    <value>환경 온도 설정 실패</value>
  </data>
  <data name="Tcp_TargetTemperatureSetSuccess" xml:space="preserve">
    <value>목표 온도 설정 성공</value>
  </data>
  <data name="Tcp_TargetTemperatureSetFailed" xml:space="preserve">
    <value>목표 온도 설정 실패</value>
  </data>
  <data name="Tcp_MissingEnableValue" xml:space="preserve">
    <value>활성화 상태 매개변수 누락</value>
  </data>
  <data name="Tcp_TecEnabled" xml:space="preserve">
    <value>TEC 활성화됨</value>
  </data>
  <data name="Tcp_TecDisabled" xml:space="preserve">
    <value>TEC 비활성화됨</value>
  </data>
  <data name="Tcp_TecEnableSetFailed" xml:space="preserve">
    <value>TEC 활성화 설정 실패</value>
  </data>
  <data name="Tcp_MaxTemperatureSetSuccess" xml:space="preserve">
    <value>최대 온도 설정 성공</value>
  </data>
  <data name="Tcp_MaxTemperatureSetFailed" xml:space="preserve">
    <value>최대 온도 설정 실패</value>
  </data>
  <data name="Tcp_MinTemperatureSetSuccess" xml:space="preserve">
    <value>최소 온도 설정 성공</value>
  </data>
  <data name="Tcp_MinTemperatureSetFailed" xml:space="preserve">
    <value>최소 온도 설정 실패</value>
  </data>
  <data name="Tcp_ReadTemperatureDataFailed" xml:space="preserve">
    <value>온도 데이터 읽기 실패</value>
  </data>
  <data name="Tcp_ReadStatusFailed" xml:space="preserve">
    <value>상태 읽기 실패: {0}</value>
  </data>
</root>
