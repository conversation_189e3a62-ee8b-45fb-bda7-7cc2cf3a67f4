﻿<UserControl x:Class="ControlTemperature.View.ControllerControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ControlTemperature.View"
             xmlns:helpers="clr-namespace:ControlTemperature.Helper"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 转换器 -->
        <helpers:BoolToColorConverter x:Key="BoolToColorConverter"/>
        
        <!-- 样式 -->
        <Style TargetType="Button">
            <Setter Property="Height" Value="30"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
        </Style>
    </UserControl.Resources>


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- 控制器信息栏 -->
        <!--第一行-->
        <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="串口号:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding PortName}" FontWeight="Bold" Foreground="#2196F3" 
                              FontSize="16" VerticalAlignment="Center" Margin="5,0,20,0"/>
                    <TextBlock Text="设备状态:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding DeviceStatus}" FontWeight="Bold" 
                              Foreground="{Binding IsConnected, Converter={StaticResource BoolToColorConverter}}" 
                              VerticalAlignment="Center" Margin="5,0,20,0"/>
                    <TextBlock Text="通讯状态:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding CommunicationStatus}" FontWeight="Bold" 
                              Foreground="{Binding IsConnected, Converter={StaticResource BoolToColorConverter}}" 
                              VerticalAlignment="Center" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 两个通道 -->
        <!--第二行-->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 通道1 - 绑定到 Channels 集合的第一个元素 -->
            <local:ChannelControl Grid.Column="0" x:Name="Channel1" 
                                   DataContext="{Binding Channels[0]}"/>
            
            <!-- 分隔线 -->
            <Border Grid.Column="1" Background="#E0E0E0" Width="2" CornerRadius="1"/>

            <!-- 通道2 - 绑定到 Channels 集合的第二个元素 -->
            <local:ChannelControl Grid.Column="2" x:Name="channel2" 
                                   DataContext="{Binding Channels[1]}"/>
        </Grid>
    </Grid>
</UserControl>
