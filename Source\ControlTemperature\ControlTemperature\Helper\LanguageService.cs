using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Resources;
using System.Threading;
using System.Windows;

namespace ControlTemperature.Helper
{
    /// <summary>
    /// 语言服务类，管理多语言切换功能
    /// </summary>
    public class LanguageService : NotifyPropertyBase
    {
        private static LanguageService _instance;
        private static readonly object _lock = new object();
        
        private CultureInfo _currentCulture;
        private ResourceManager _resourceManager;
        private const string LanguageSettingsFile = "language.config";
        
        /// <summary>
        /// 单例实例
        /// </summary>
        public static LanguageService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LanguageService();
                        }
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// 语言变更事件
        /// </summary>
        public event EventHandler LanguageChanged;
        
        /// <summary>
        /// 当前文化信息
        /// </summary>
        public CultureInfo CurrentCulture
        {
            get => _currentCulture;
            private set
            {
                _currentCulture = value;
                RaisePropertyChanged();
                RaisePropertyChanged(nameof(CurrentLanguageName));
            }
        }
        
        /// <summary>
        /// 当前语言名称
        /// </summary>
        public string CurrentLanguageName
        {
            get
            {
                switch (_currentCulture?.Name)
                {
                    case "zh-CN":
                        return "中文";
                    case "en-US":
                        return "English";
                    case "ko-KR":
                        return "한국어";
                    default:
                        return "中文";
                }
            }
        }
        
        /// <summary>
        /// 支持的语言列表
        /// </summary>
        public List<LanguageInfo> SupportedLanguages { get; private set; }
        
        private LanguageService()
        {
            InitializeSupportedLanguages();
            LoadSavedLanguage();
        }
        
        /// <summary>
        /// 初始化支持的语言列表
        /// </summary>
        private void InitializeSupportedLanguages()
        {
            SupportedLanguages = new List<LanguageInfo>
            {
                new LanguageInfo { Code = "zh-CN", Name = "中文", Culture = new CultureInfo("zh-CN") },
                new LanguageInfo { Code = "en-US", Name = "English", Culture = new CultureInfo("en-US") },
                new LanguageInfo { Code = "ko-KR", Name = "한국어", Culture = new CultureInfo("ko-KR") }
            };
        }
        
        /// <summary>
        /// 加载保存的语言设置
        /// </summary>
        private void LoadSavedLanguage()
        {
            try
            {
                string configPath = Path.Combine(Gobal.BinPatn, LanguageSettingsFile);
                if (File.Exists(configPath))
                {
                    string savedLanguage = File.ReadAllText(configPath).Trim();
                    var language = SupportedLanguages.FirstOrDefault(l => l.Code == savedLanguage);
                    if (language != null)
                    {
                        SetLanguage(language.Code);
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelp.System($"加载语言设置失败: {ex.Message}", LogLevel.Warning);
            }
            
            // 默认使用中文
            SetLanguage("zh-CN");
        }
        
        /// <summary>
        /// 保存语言设置
        /// </summary>
        private void SaveLanguageSetting()
        {
            try
            {
                string configPath = Path.Combine(Gobal.BinPatn, LanguageSettingsFile);
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));
                File.WriteAllText(configPath, _currentCulture.Name);
            }
            catch (Exception ex)
            {
                LogHelp.System($"保存语言设置失败: {ex.Message}", LogLevel.Warning);
            }
        }
        
        /// <summary>
        /// 设置语言
        /// </summary>
        /// <param name="languageCode">语言代码</param>
        public void SetLanguage(string languageCode)
        {
            try
            {
                var language = SupportedLanguages.FirstOrDefault(l => l.Code == languageCode);
                if (language == null)
                {
                    LogHelp.System($"不支持的语言代码: {languageCode}", LogLevel.Warning);
                    return;
                }
                
                // 设置当前文化
                CurrentCulture = language.Culture;
                
                // 设置线程文化
                Thread.CurrentThread.CurrentCulture = language.Culture;
                Thread.CurrentThread.CurrentUICulture = language.Culture;
                
                // 设置应用程序文化
                CultureInfo.DefaultThreadCurrentCulture = language.Culture;
                CultureInfo.DefaultThreadCurrentUICulture = language.Culture;
                
                // 加载对应的资源管理器
                LoadResourceManager(languageCode);
                
                // 保存设置
                SaveLanguageSetting();
                
                // 触发语言变更事件
                LanguageChanged?.Invoke(this, EventArgs.Empty);
                
                LogHelp.System($"语言已切换到: {language.Name}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                LogHelp.System($"设置语言失败: {ex.Message}", LogLevel.Error);
            }
        }
        
        /// <summary>
        /// 加载资源管理器
        /// </summary>
        /// <param name="languageCode">语言代码</param>
        private void LoadResourceManager(string languageCode)
        {
            try
            {
                string resourceName = $"ControlTemperature.Resources.Resources.{languageCode}";
                _resourceManager = new ResourceManager(resourceName, typeof(LanguageService).Assembly);
                
                // 测试资源是否可用
                string testString = _resourceManager.GetString("MainWindow_Title");
                if (string.IsNullOrEmpty(testString))
                {
                    LogHelp.System($"资源文件 {resourceName} 可能不存在或为空", LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                LogHelp.System($"加载资源管理器失败: {ex.Message}", LogLevel.Error);
                // 如果加载失败，尝试加载默认中文资源
                if (languageCode != "zh-CN")
                {
                    LoadResourceManager("zh-CN");
                }
            }
        }
        
        /// <summary>
        /// 获取本地化字符串
        /// </summary>
        /// <param name="key">资源键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>本地化字符串</returns>
        public string GetString(string key, string defaultValue = null)
        {
            try
            {
                if (_resourceManager == null)
                {
                    return defaultValue ?? key;
                }
                
                string value = _resourceManager.GetString(key);
                return !string.IsNullOrEmpty(value) ? value : (defaultValue ?? key);
            }
            catch (Exception ex)
            {
                LogHelp.System($"获取本地化字符串失败 [{key}]: {ex.Message}", LogLevel.Warning);
                return defaultValue ?? key;
            }
        }
        
        /// <summary>
        /// 获取格式化的本地化字符串
        /// </summary>
        /// <param name="key">资源键</param>
        /// <param name="args">格式化参数</param>
        /// <returns>格式化的本地化字符串</returns>
        public string GetFormattedString(string key, params object[] args)
        {
            try
            {
                string format = GetString(key);
                return string.Format(format, args);
            }
            catch (Exception ex)
            {
                LogHelp.System($"格式化本地化字符串失败 [{key}]: {ex.Message}", LogLevel.Warning);
                return key;
            }
        }
    }
    
    /// <summary>
    /// 语言信息类
    /// </summary>
    public class LanguageInfo
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public CultureInfo Culture { get; set; }
        
        public override string ToString()
        {
            return Name;
        }
    }
}
