﻿using ControlTemperature.Helper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ControlTemperature.ViewModel
{
    public class ControllerControlViewModel : NotifyPropertyBase
    {
        private int _driverIndex;
        private TecModbusData _connectionData;

        public ControllerControlViewModel()
        {
            InitializeChannels();
        }

        public ControllerControlViewModel(int driverIndex)
        {
            _driverIndex = driverIndex;
            
            // 获取连接配置数据
            var connectionDataArray = Gobal.FileData.TecRTUDataLoad();
            if (connectionDataArray != null && driverIndex < connectionDataArray.Length)
            {
                _connectionData = connectionDataArray[driverIndex];
                
                // 设置控制器属性
                ControllerAddress = _connectionData.SlaveId;
                ControllerHeader = $"控制器{driverIndex + 1} (地址:{_connectionData.SlaveId})";
                PortName = _connectionData.Port ?? "未知";
            }
            else
            {
                // 如果没有配置数据，使用默认值
                ControllerAddress = driverIndex + 1;
                ControllerHeader = $"控制器{driverIndex + 1} (地址:{driverIndex + 1})";
                PortName = "未知";
            }
            
            InitializeChannels();
        }

        /// <summary>
        /// 初始化通道
        /// </summary>
        private void InitializeChannels()
        {
            // 初始化 Channels 集合
            Channels = new ObservableCollection<ChannelControlViewModel>();
            
            // 添加两个默认通道
            var channel1 = new ChannelControlViewModel
            {
                ChannelNumber = "1",
                ChannelName = "通道1",
                HeaderColor = "#4CAF50"
            };
            channel1.SetDriverInfo(_driverIndex, 1);
            Channels.Add(channel1);
            
            var channel2 = new ChannelControlViewModel
            {
                ChannelNumber = "2",
                ChannelName = "通道2", 
                HeaderColor = "#2196f3"
            };
            channel2.SetDriverInfo(_driverIndex, 2);
            Channels.Add(channel2);
        }

        private int controllerAddress = 1;
        /// <summary>
        /// 控制器地址
        /// </summary>
        public int ControllerAddress
        {
            get => controllerAddress;
            set
            {
                controllerAddress = value;
                RaisePropertyChanged();
            }
        }

        private string controllerHeader = "控制器1 (地址:1)";
        /// <summary>
        /// 控制器标签页标题
        /// </summary>
        public string ControllerHeader
        {
            get => controllerHeader;
            set
            {
                controllerHeader = value;
                RaisePropertyChanged();
            }
        }

        private string deviceStatus = "离线";
        /// <summary>
        /// 设备状态
        /// </summary>
        public string DeviceStatus
        {
            get => deviceStatus;
            set
            {
                deviceStatus = value;
                RaisePropertyChanged();
            }
        }

        private string communicationStatus = "未连接";
        /// <summary>
        /// 通讯状态
        /// </summary>
        public string CommunicationStatus
        {
            get => communicationStatus;
            set
            {
                communicationStatus = value;
                RaisePropertyChanged();
            }
        }

        private string portName = "未知";
        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName
        {
            get => portName;
            set
            {
                portName = value;
                RaisePropertyChanged();
            }
        }

        private bool isConnected = false;
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get => isConnected;
            set
            {
                isConnected = value;
                RaisePropertyChanged();
                
                // 更新状态显示
                DeviceStatus = value ? "在线" : "离线";
                CommunicationStatus = value ? "正常" : "未连接";
            }
        }

        /// <summary>
        /// 通道集合
        /// </summary>
        private ObservableCollection<ChannelControlViewModel> channels;
        public ObservableCollection<ChannelControlViewModel> Channels
        {
            get => channels;
            set
            {
                channels = value;
                RaisePropertyChanged();
            }
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        public void UpdateConnectionStatus()
        {
            try
            {
                if (_connectionData != null)
                {
                    // 确保端口名称是最新的
                    PortName = _connectionData.Port ?? "未知";
                    
                    // 检查连接状态
                    bool connected = Gobal.modbusTec.IsDriverConnected(_driverIndex);
                    
                    // 更新连接状态
                    IsConnected = connected;
                    
                    LogHelp.TecCommunication($"控制器 {ControllerAddress} (索引:{_driverIndex}) 连接状态: {(connected ? "已连接" : "未连接")}");
                    
                    // 如果连接成功，初始化温度上下限
                    if (IsConnected)
                    {
                        foreach (var channel in Channels)
                        {
                            channel.InitializeTemperatureLimits();
                        }
                    }
                }
                else
                {
                    LogHelp.TecCommunication($"控制器 {ControllerAddress} 没有配置数据");
                    IsConnected = false;
                }
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"更新控制器 {ControllerAddress} 连接状态失败：{ex.Message}");
                IsConnected = false;
            }
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        public void UpdateData()
        {
            if (!IsConnected || _connectionData == null)
                return;

            try
            {
                // 更新所有通道数据
                foreach (var channel in Channels)
                {
                    channel.UpdateData();
                }
            }
            catch (Exception ex)
            {
                LogHelp.TecCommunication($"更新控制器 {ControllerAddress} 数据失败：{ex.Message}");
            }
        }

    }
}
