using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace ControlTemperature.Helper
{
    /// <summary>
    /// 布尔值到连接状态文本转换器
    /// </summary>
    public class BoolToConnectionStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isConnected)
            {
                return isConnected ? LanguageService.Instance.GetString("Status_Connected", "已连接") : LanguageService.Instance.GetString("Status_Disconnected", "未连接");
            }
            return LanguageService.Instance.GetString("Status_Unknown", "未知");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到颜色转换器
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isConnected)
            {
                return isConnected ? Brushes.Green : Brushes.Red;
            }
            return Brushes.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到加热模式转换器
    /// </summary>
    public class BoolToHeatingModeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isHeating)
            {
                return isHeating ? LanguageService.Instance.GetString("Status_Heating", "加热") : LanguageService.Instance.GetString("Status_Cooling", "制冷");
            }
            return LanguageService.Instance.GetString("Status_Unknown", "未知");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到风扇状态转换器
    /// </summary>
    public class BoolToFanStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool fanRunning)
            {
                return fanRunning ? LanguageService.Instance.GetString("Status_Running", "运行") : LanguageService.Instance.GetString("Status_Stopped", "停止");
            }
            return LanguageService.Instance.GetString("Status_Unknown", "未知");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到反转布尔值转换器
    /// </summary>
    public class BoolToInverseBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
} 