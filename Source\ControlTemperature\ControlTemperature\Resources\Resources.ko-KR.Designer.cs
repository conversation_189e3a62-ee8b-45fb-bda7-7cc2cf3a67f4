//------------------------------------------------------------------------------
// <auto-generated>
//     이 코드는 도구를 사용하여 생성되었습니다.
//     런타임 버전:4.0.30319.42000
//
//     파일에 대한 변경 사항은 잘못된 동작을 발생시킬 수 있으며, 코드를 다시 생성하면
//     이러한 변경 사항이 손실됩니다.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ControlTemperature.Resources {
    using System;
    
    
    /// <summary>
    ///   지역화된 문자열 등을 찾기 위한 강력한 형식의 리소스 클래스입니다.
    /// </summary>
    // 이 클래스는 ResGen 또는 Visual Studio와 같은 도구를 통해 StronglyTypedResourceBuilder
    // 클래스에서 자동으로 생성되었습니다.
    // 멤버를 추가하거나 제거하려면 .ResX 파일을 편집한 다음 /str 옵션을 사용하여 ResGen을
    // 다시 실행하거나 VS 프로젝트를 다시 빌드하십시오.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources_ko_KR {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources_ko_KR() {
        }
        
        /// <summary>
        ///   이 클래스에서 사용하는 캐시된 ResourceManager 인스턴스를 반환합니다.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ControlTemperature.Resources.Resources.ko-KR", typeof(Resources_ko_KR).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   이 강력한 형식의 리소스 클래스를 사용하여 모든 리소스 조회에 대해
        ///   현재 스레드의 CurrentUICulture 속성을 재정의합니다.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   활성화와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_Enable {
            get {
                return ResourceManager.GetString("Channel_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   비상정지와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_EmergencyStop {
            get {
                return ResourceManager.GetString("Channel_EmergencyStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   현재 온도와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_CurrentTemperature {
            get {
                return ResourceManager.GetString("Channel_CurrentTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   목표 온도와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_TargetTemperature {
            get {
                return ResourceManager.GetString("Channel_TargetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   온도 설정과 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_TemperatureSettings {
            get {
                return ResourceManager.GetString("Channel_TemperatureSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   설정 온도:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_SetTemperature {
            get {
                return ResourceManager.GetString("Channel_SetTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   최대 온도:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_MaxTemperature {
            get {
                return ResourceManager.GetString("Channel_MaxTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   최소 온도:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_MinTemperature {
            get {
                return ResourceManager.GetString("Channel_MinTemperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   설정과 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_SetButton {
            get {
                return ResourceManager.GetString("Channel_SetButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   출력 상태와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_OutputStatus {
            get {
                return ResourceManager.GetString("Channel_OutputStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   실행 모드:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_RunningMode {
            get {
                return ResourceManager.GetString("Channel_RunningMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   팬 상태:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_FanStatus {
            get {
                return ResourceManager.GetString("Channel_FanStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   실시간 로그와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_RealtimeLog {
            get {
                return ResourceManager.GetString("Channel_RealtimeLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   지우기와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string Channel_ClearLog {
            get {
                return ResourceManager.GetString("Channel_ClearLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   온도 제어 플랫폼 시스템과 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_Title {
            get {
                return ResourceManager.GetString("MainWindow_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   장치 연결과 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_ConnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_ConnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   장치 연결 해제와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_DisconnectDevice {
            get {
                return ResourceManager.GetString("MainWindow_DisconnectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   연결 상태:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_ConnectionStatus {
            get {
                return ResourceManager.GetString("MainWindow_ConnectionStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   활성 장치:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_ActiveDevices {
            get {
                return ResourceManager.GetString("MainWindow_ActiveDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   TCP 서버:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_TcpServer {
            get {
                return ResourceManager.GetString("MainWindow_TcpServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   언어:와 유사한 지역화된 문자열을 찾습니다.
        /// </summary>
        internal static string MainWindow_Language {
            get {
                return ResourceManager.GetString("MainWindow_Language", resourceCulture);
            }
        }
    }
}
