<?xml version="1.0" encoding="utf-8"?>
<installer-script minSpecVersion="1.000000" authoringTool="com.apple.PackageMaker" authoringToolVersion="3.0.4" authoringToolBuild="179">
    <title>FTDIUSBSerialDriverInstaller</title>
    <options customize="allow" allow-external-scripts="no" rootVolumeOnly="true"/>
    <installation-check script="pm_install_check();"/>
    <script>function pm_install_check() {
  if(!(/* &lt; */ system.compareVersions(system.version.ProductVersion, '10.4') &lt; 0)) {
    my.result.title = 'Wrong OS Version';
    my.result.message = 'This driver is only intended for Mac OS X systems running 10.3.  Please run the FTDIUSBSerialDriver_10_4_10_5_10_6_10_7 installer.';
    my.result.type = 'Fatal';
    return false;
  }
  return true;
}
</script>
    <background file="background" alignment="center" scaling="none"/>
    <readme file="ReadMe"/>
    <choices-outline>
        <line choice="choice0"/>
    </choices-outline>
    <choice id="choice0" title="FTDIUSBSerialDriver_10_3">
        <pkg-ref id="com.FTDI.ftdiusbserialdriverinstaller_10_3.FTDIUSBSerialDriver.pkg"/>
    </choice>
    <pkg-ref id="com.FTDI.ftdiusbserialdriverinstaller_10_3.FTDIUSBSerialDriver.pkg" installKBytes="292" version="1.0" auth="Root" onConclusion="RequireRestart">file:./Contents/Packages/ftdiusbserialdriver.pkg</pkg-ref>
</installer-script>