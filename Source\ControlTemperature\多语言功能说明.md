# WPF温控平台多语言功能说明

## 功能概述

本项目已成功添加了中文、英文、韩文三种语言的切换功能。用户可以在主界面右上角的语言下拉框中选择不同的语言，系统会立即切换到对应的语言界面。

## 支持的语言

1. **中文 (zh-CN)** - 默认语言
2. **English (en-US)** - 英语
3. **한국어 (ko-KR)** - 韩语

## 功能特点

### 1. 完整的界面本地化
- 主窗口标题和所有按钮文本
- 控制器信息显示（串口号、设备状态、通讯状态）
- 通道控制界面（使能、急停、温度设置等）
- 状态显示（连接状态、运行模式、风扇状态等）
- 日志消息和错误提示
- TCP服务器通信消息

### 2. 实时语言切换
- 无需重启应用程序
- 切换后所有界面元素立即更新
- 保持当前操作状态不变

### 3. 语言偏好保存
- 用户选择的语言会自动保存到配置文件
- 下次启动应用程序时自动加载上次选择的语言

## 使用方法

### 切换语言
1. 在主窗口右上角找到"语言:"标签
2. 点击下拉框选择所需语言：
   - 中文
   - English  
   - 한국어
3. 选择后界面会立即切换到对应语言

### 语言配置文件
语言设置保存在：`Config/language.config`
- 文件内容为语言代码（如：zh-CN、en-US、ko-KR）
- 可以手动编辑此文件来设置默认语言

## 技术实现

### 1. 资源文件结构
```
Resources/
├── Resources.zh-CN.resx     # 中文资源
├── Resources.en-US.resx     # 英文资源
├── Resources.ko-KR.resx     # 韩文资源
├── Resources.zh-CN.Designer.cs
├── Resources.en-US.Designer.cs
└── Resources.ko-KR.Designer.cs
```

### 2. 核心组件
- **LanguageService**: 语言服务单例类，管理语言切换逻辑
- **LanguageInfo**: 语言信息类，包含语言代码、名称和文化信息
- **资源绑定**: 所有UI文本都通过资源绑定实现动态切换

### 3. 架构特点
- 单例模式的语言服务确保全局一致性
- 事件驱动的语言变更通知机制
- MVVM模式下的数据绑定自动更新
- 线程安全的资源访问

## 扩展说明

### 添加新语言
1. 在`Resources`文件夹中添加新的`.resx`文件（如：`Resources.fr-FR.resx`）
2. 在`LanguageService.cs`的`InitializeSupportedLanguages`方法中添加新语言信息
3. 翻译所有资源键对应的文本内容

### 添加新的本地化文本
1. 在所有语言的`.resx`文件中添加新的资源键和对应翻译
2. 在需要使用的地方调用`LanguageService.Instance.GetString("ResourceKey", "DefaultValue")`
3. 如果是UI绑定，创建对应的属性并在语言变更时通知更新

## 注意事项

1. **资源键命名规范**: 使用分类前缀，如`MainWindow_`, `Channel_`, `Status_`等
2. **默认值**: 所有`GetString`调用都应提供默认值，确保在资源缺失时有备用文本
3. **格式化字符串**: 对于包含参数的文本，使用`GetFormattedString`方法
4. **UI线程安全**: 语言切换会自动在UI线程上执行，确保界面更新的安全性

## 测试建议

1. 测试所有三种语言的切换功能
2. 验证语言偏好的保存和加载
3. 检查所有界面元素是否正确翻译
4. 测试应用程序重启后语言设置的持久性
5. 验证日志消息和错误提示的本地化

## 维护说明

- 定期检查新增功能是否添加了对应的多语言支持
- 保持三种语言资源文件的同步更新
- 注意文本长度差异对界面布局的影响
- 考虑不同语言的文化习惯和表达方式
